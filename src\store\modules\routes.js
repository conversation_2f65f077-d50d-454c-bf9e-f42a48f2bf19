/**
 * @description 路由拦截状态管理，目前两种模式：all模式与intelligence模式，其中partialRoutes是菜单暂未使用
 */
import { getRouterList } from '@/api/user'
import { authentication, rolesControl } from '@/config'
import { asyncRoutes, constantRoutes, resetRouter } from '@/router'
import { convertRouter, filterRoutes } from '@/utils/routes'
import { isArray } from '@/utils/validate'
import Vue from 'vue'

const state = () => ({
  routes: [],
  activeName: '',
  // 新增：主导航数据
  mainNavs: [],
  // 新增：当前激活的主导航
  activeMainNav: '',
  // 新增：当前显示的子导航数据
  currentSubNavs: [],
})
const getters = {
  routes: (state) => state.routes,
  activeName: (state) => state.activeName,
  // 新增：主导航数据
  mainNavs: (state) => state.mainNavs,
  // 新增：当前激活的主导航
  activeMainNav: (state) => state.activeMainNav,
  // 新增：当前显示的子导航数据
  currentSubNavs: (state) => state.currentSubNavs,
}
const mutations = {
  /**
   * @description 多模式设置路由
   * @param {*} state
   * @param {*} routes
   */
  setRoutes(state, routes) {
    state.routes = routes
  },
  /**
   * @description 修改Meta
   * @param {*} state
   * @param options
   */
  changeMenuMeta(state, options) {
    function handleRoutes(routes) {
      return routes.map((route) => {
        if (route.name === options.name) Object.assign(route.meta, options.meta)
        if (route.children && route.children.length)
          route.children = handleRoutes(route.children)
        return route
      })
    }
    state.routes = handleRoutes(state.routes)
  },
  /**
   * @description 修改 activeName
   * @param {*} state
   * @param activeName 当前激活菜单
   */
  changeActiveName(state, activeName) {
    state.activeName = activeName
  },
  /**
   * @description 设置主导航数据
   * @param {*} state
   * @param mainNavs 主导航数据
   */
  setMainNavs(state, mainNavs) {
    state.mainNavs = mainNavs
  },
  /**
   * @description 设置当前激活的主导航
   * @param {*} state
   * @param activeMainNav 当前激活的主导航
   */
  setActiveMainNav(state, activeMainNav) {
    state.activeMainNav = activeMainNav
  },
  /**
   * @description 设置当前显示的子导航数据
   * @param {*} state
   * @param currentSubNavs 当前显示的子导航数据
   */
  setCurrentSubNavs(state, currentSubNavs) {
    state.currentSubNavs = currentSubNavs
  },
}
const actions = {
  /**
   * @description 多模式设置路由
   * @param {*} { commit }
   * @param mode
   * @returns
   */
  async setRoutes({ commit }, mode = 'none') {
    // 默认前端路由
    let asyncRoutesAll = asyncRoutes
    let routes = [...asyncRoutesAll]
    // 设置游客路由关闭路由拦截(不需要可以删除)
    const control = mode === 'visit' ? false : rolesControl
    // 设置后端路由(不需要可以删除)
    if (authentication === 'all') {
      const {
        data: { data, code },
      } = await getRouterList({
        clientId: 'hse-accident-event',
      })
      if (code != '00000' || data.length == 0) {
        Vue.prototype.$baseMessage(
          '该账号没有授权，请联系管理员！',
          'error',
          'vab-hey-message-error'
        )
      }
      var list = data
      if (!isArray(list))
        Vue.prototype.$baseMessage(
          '路由格式返回有误！',
          'error',
          'vab-hey-message-error'
        )
      if (list[list.length - 1].path !== '*')
        list.push({ path: '*', redirect: '/404', meta: { hidden: true } })
      routes = convertRouter(list)
      // 根据权限和rolesControl过滤路由
      const accessRoutes = filterRoutes([...constantRoutes, ...routes], control)
      // 设置菜单所需路由
      commit('setRoutes', accessRoutes)
      // 处理主导航和子导航数据
      const mainNavs = routes.map((route) => ({
        name: route.name,
        path: route.path,
        title: route.meta ? route.meta.title : '',
        icon: route.meta ? route.meta.icon : '',
        children: route.children || [],
      }))
      commit('setMainNavs', mainNavs)
      // 设置默认激活的主导航（第一个）
      if (mainNavs.length > 0) {
        commit('setActiveMainNav', mainNavs[0].name)
        // 将所有children扁平化处理，作为侧边栏导航数据
        const allSubNavs = mainNavs.flatMap((nav) => nav.children || [])
        commit('setCurrentSubNavs', allSubNavs)
      }
      // 根据可访问路由重置Vue Router
      await resetRouter(accessRoutes)
    } else {
      // 根据权限和rolesControl过滤路由
      const accessRoutes = filterRoutes([...constantRoutes, ...routes], control)
      // 设置菜单所需路由
      commit('setRoutes', accessRoutes)
      // 根据可访问路由重置Vue Router
      await resetRouter(accessRoutes)
    }
  },
  /**
   * @description 修改Route Meta
   * @param {*} { commit }
   * @param options
   */
  changeMenuMeta({ commit }, options = {}) {
    commit('changeMenuMeta', options)
  },
  /**
   * @description 修改 activeName
   * @param {*} { commit }
   * @param activeName 当前激活菜单
   */
  changeActiveName({ commit }, activeName) {
    commit('changeActiveName', activeName)
  },
  /**
   * @description 切换主导航
   * @param {*} { commit, getters }
   * @param mainNavName 主导航名称
   */
  switchMainNav({ commit, getters }, mainNavName) {
    const mainNavs = getters.mainNavs
    const targetNav = mainNavs.find((nav) => nav.name === mainNavName)
    if (targetNav) {
      commit('setActiveMainNav', mainNavName)
      // 始终显示所有children，而不是只显示当前选中主导航的children
      const allSubNavs = mainNavs.flatMap((nav) => nav.children || [])
      commit('setCurrentSubNavs', allSubNavs)
    }
  },
}
export default { state, getters, mutations, actions }
