/**
 * @description router全局配置，如有必要可分文件抽离，其中asyncRoutes只有在intelligence模式下才会用到，pro版只支持remixIcon图标，具体配置请查看vip群文档
 */
import { publicPath, routerMode } from '@/config'
import Layout from '@/vab/layouts'
import Vue from 'vue'
import VueRouter from 'vue-router'
import store from '@/store'

Vue.use(VueRouter)
export const constantRoutes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index1'),
    meta: {
      hidden: true,
    },
  },
  {
    path: '/register',
    component: () => import('@/views/register'),
    meta: {
      hidden: true,
    },
  },
  {
    path: '/callback',
    component: () => import('@/views/callback'),
    meta: {
      hidden: true,
    },
  },
  {
    path: '/403',
    name: '403',
    component: () => import('@/views/403'),
    meta: {
      hidden: true,
    },
  },
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/404'),
    meta: {
      hidden: true,
    },
  },
  {
    path: '/code',
    name: 'code',
    component: () => import('@/views/code'),
    meta: {
      hidden: true,
    },
  },
  {
    path: '/',
    name: 'Root',
    component: Layout,
    meta: {
      title: '首页',
      icon: 'home-2-line',
      breadcrumbHidden: true,
    },
    children: [
      {
        path: 'index',
        name: 'Index',
        component: () => import('@/views/index'),
        meta: {
          title: '首页',
          icon: 'home-2-line',
          noClosable: true,
        },
      },
      {
        path: 'myAgentMatters',
        component: () => import('@/views/AccidentEvent/agentMatters/my'),
        name: 'MyAgentMatters',
        meta: {
          title: '我的待办事项',
          icon: 'list-ordered',
          hidden: true,
        },
      },
      {
        path: 'PersonalCenter',
        name: 'PersonalCenter',
        component: () => import('@/views/system/personalCenter/index'),
        meta: {
          title: '个人中心',
          icon: 'file-user-line',
          noClosable: true,
        },
      },
    ],
  },
]

export const asyncRoutes = [
  {
    path: '/system',
    name: 'System',
    component: Layout,
    meta: {
      title: '系统配置',
      icon: 'user-settings-line',
    },
    children: [
      {
        path: 'appManagement',
        name: 'APPManagement',
        component: () => import('@/views/system/appManagement'),
        meta: {
          title: 'APP版本管理',
          icon: 'file-shield-2-line',
        },
      },
      {
        path: 'personalCenter',
        name: 'PersonalCenter',
        component: () => import('@/views/system/personalCenter'),
        meta: {
          title: '个人中心',
          icon: 'map-pin-user-line',
          hidden: true,
        },
      },
      {
        path: 'systemLog',
        name: 'SystemLog',
        component: () => import('@/views/system/systemLog'),
        meta: {
          title: '系统日志',
          icon: 'file-shield-2-line',
        },
      },
    ],
  },

  {
    path: '*',
    redirect: '/404',
    meta: {
      hidden: true,
    },
  },
]

const router = createRouter()

// function fatteningRoutes(routes) {
//   return routes.flatMap((route) => {
//     return route.children ? fatteningRoutes(route.children) : route
//   })
// }

export function resetRouter(routes = constantRoutes) {
  // routes.map((route) => {
  //   if (route.children) {
  //     route.children = fatteningRoutes(route.children)
  //   }
  // })
  router.matcher = createRouter(routes).matcher
}

function createRouter(routes = constantRoutes) {
  return new VueRouter({
    base: publicPath,
    mode: routerMode,
    scrollBehavior: () => ({
      y: 0,
    }),
    routes: routes,
  })
}

// 添加全局前置守卫
router.beforeEach(async (to, from, next) => {
  // 白名单路径直接放行
  if (
    to.path === '/login' ||
    to.path === '/register' ||
    to.path === '/404' ||
    to.path === '/403'
  ) {
    return next()
  }

  try {
    const userList = store.state.user.userList

    // 如果用户列表为空，获取用户列表
    if (!userList || userList.length === 0) {
      console.log('预加载用户列表数据')
      // 使用Vue 2.7支持的async/await特性
      await store.dispatch('user/getUserList')
    }

    // 数据加载完成后放行
    next()
  } catch (error) {
    console.error('路由守卫中出现错误:', error)
    // 发生错误时也要放行，避免路由卡死
    next()
  }
})

const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch((err) => err)
}

export default router
