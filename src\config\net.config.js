/**
 * @description 导出网络配置
 **/

module.exports = {
  //环境接口在对应的配置文件中配置
  serverURL: process.env.VUE_APP_serverURL,
  baseURL: process.env.VUE_APP_baseURL,
  buketName: process.env.VUE_APP_buketName,
  // 配后端数据的接收方式application/json;charset=UTF-8 或 application/x-www-form-urlencoded;charset=UTF-8
  contentType: 'application/json;charset=UTF-8',
  // 最长请求时间
  requestTimeout: 60000,
  // 操作正常code，支持String、Array、int多种类型
  successCode: [200, 0, '200', '0'],
  // 数据状态的字段名称
  statusName: 'code',
  // 状态信息的字段名称
  messageName: 'message',
}
