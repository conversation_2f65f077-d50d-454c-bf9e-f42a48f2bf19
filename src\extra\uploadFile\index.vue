<template>
  <div style="width: 100%">
    <!-- 按钮类型 -->
    <el-upload
      v-if="uploadType == 'btn'"
      :accept="accepted"
      :action="uploadUrl"
      :auto-upload="true"
      :before-upload="beforeAvatarUpload"
      :file-list="fileList"
      :headers="headerObj"
      :limit="limit"
      multiple
      :on-error="handleError"
      :on-exceed="handleExceed"
      :on-preview="handlePreview"
      :on-remove="handleRemove"
      :on-success="handleSuccess"
      :show-file-list="showFileList"
    >
      <el-button size="mini" type="primary">点击上传</el-button>
      <div slot="tip" class="el-upload__tip">
        {{ accepted ? `只能上传${accepted}格式文件` : ''
        }}{{
          maxSize && sizeUnit
            ? `，且文件大小不超过${maxSize}${sizeUnit}。`
            : ''
        }}选择文件后，将自动上传
      </div>
    </el-upload>
    <el-upload
      v-else
      ref="upload"
      :accept="accepted"
      :action="uploadUrl"
      :auto-upload="true"
      :before-upload="beforeAvatarUpload"
      class="upload-demo"
      drag
      :file-list="fileList"
      :headers="headerObj"
      :limit="limit"
      multiple
      :on-error="handleError"
      :on-exceed="handleExceed"
      :on-preview="handlePreview"
      :on-remove="handleRemove"
      :on-success="handleSuccess"
      :show-file-list="showFileList"
    >
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">
        <!-- 将文件拖到此处，或 -->
        <em>点击上传</em>
      </div>
      <div class="el-upload__tip" slot="tip">
        {{ accepted ? `只能上传${accepted}格式文件` : ''
        }}{{
          maxSize && sizeUnit
            ? `，且文件大小不超过${maxSize}${sizeUnit}。`
            : ''
        }}选择文件后，将自动上传
      </div>
    </el-upload>
    <el-dialog
      append-to-body
      class="dialogTips"
      :close-on-click-modal="false"
      :show-close="false"
      :visible.sync="dialogVisibleTips"
      width="300px"
    >
      <p>文件正在上传，请稍候...</p>
    </el-dialog>
  </div>
</template>

<script>
  // import { baseURL } from '@/config'
  import { serverURL } from '@/config'
  import { getToken } from '@/utils/token'
  import moment from 'moment'
  export default {
    name: 'Upload',
    props: {
      fileList: {
        type: Array,
        default: () => [],
      },
      params: {
        type: Object,
        default: () => {},
      },
      limit: {
        type: Number,
        default: () => null,
      },
      accepted: {
        type: String,
        default: function () {
          return ''
        },
      },
      showFileList: {
        type: Boolean,
        default: function () {
          return true
        },
      },
      //上传文件大小，同时传单位才有效
      maxSize: {
        type: Number,
        default: function () {
          return 0
        },
      },
      // 上传文件大小单位
      sizeUnit: {
        type: String,
        default: function () {
          return ''
        },
      },
      //上传样式类型：按钮btn、图片picture、拖拽drag（默认）
      uploadType: {
        type: String,
        default: function () {
          return 'drag'
        },
      },
    },
    data() {
      return {
        imageUrl: '',
        dialogImageUrl: '',
        dialogVisible: false,
        uploadUrl: `${serverURL}/swisp-base-service/api/v1/oss/ali/upload?subPath=attachments/${moment().format(
          'YYYY/MM/DD'
        )}&buketName=${'hse-accident-event'}`,

        headerObj: {
          Authorization: `${getToken()}`,
        },

        files: [],
        filePath: [],
        list: this.fileList,
        filename: '',
        dialogVisibleTips: false,
      }
    },
    watch: {},
    methods: {
      handleSuccess(res) {
        if (res.code == '00000') {
          this.imageUrl = res.data.path
          this.filePath.push(res.data.path)
          this.$emit(
            'uploadsuccess',
            { ...res.data, filename: this.filename },
            this.params,
            'add'
          )
        } else {
          this.$baseMessage(res.msg)
        }
        this.dialogVisibleTips = false
      },
      handlePreview(file) {
        window.open(file.url)
      },
      beforeAvatarUpload(file) {
        this.filename = file.name
        let isSize = true
        // 1、限制大小
        if (this.maxSize && this.sizeUnit) {
          if (this.sizeUnit == 'MB') {
            isSize = file.size / 1024 / 1024 < this.maxSize
          }
          if (this.sizeUnit == 'KB') {
            isSize = file.size / 1024 < this.maxSize
          }
          if (!isSize) {
            this.$message.error(
              `上传文件大小不能超过 ${this.maxSize}${this.sizeUnit}!`
            )
          }
        }
        if (isSize) {
          // 显示上传等待提示
          this.dialogVisibleTips = true
        }
        return isSize
      },
      handleRemove(file, fileList) {
        this.filePath = fileList.map((item) => {
          return item.url
        })
        this.$emit('uploadsuccess', file, this.params, 'remove')
      },
      handleExceed(files, fileList) {
        if (this.limit > 0) {
          this.$message.warning(
            `当前限制选择 ${this.limit} 个文件，本次选择了 ${
              files.length
            } 个文件，共选择了 ${files.length + fileList.length} 个文件`
          )
        }
      },
      handleError(err, file, fileList) {
        console.log(err, file, fileList)
        this.dialogVisibleTips = false
      },
      clearFiles() {
        this.$refs.upload.clearFiles()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .upload-demo {
    ::v-deep {
      .el-upload-dragger {
        width: 100% !important;
        height: 70px;
        .el-icon-upload {
          margin: 0;
          font-size: 22px;
          line-height: 38px;
        }
      }
      .el-upload {
        width: 100% !important;
      }
    }
  }
  /* .upload-demo ::v-deep {
  } */
  .dialogTips {
    ::v-deep {
      .el-dialog__header {
        padding: 0;
      }
      .el-dialog__body {
        padding: 10px;
      }
    }
  }
</style>
