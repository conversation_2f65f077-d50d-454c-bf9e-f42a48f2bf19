<template>
  <div>
    <el-dialog
      :before-close="handleClose"
      center
      :close-on-click-modal="false"
      custom-class="safety-investigation-dialog"
      :title="'重要生产安全事件调查单'"
      :visible.sync="dialogVisible"
      width="80%"
    >
      <div class="investigation-form">
        <el-form
          ref="investigationForm"
          label-width="180px"
          :model="form"
          :rules="rules"
        >
          <div class="form-card">
            <div class="section-header">基本信息</div>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="事件发生单位：" prop="unitId">
                  <el-input
                    v-model="form.unitId"
                    placeholder="请输入"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="承包商名称：" prop="contractorName">
                  <el-input
                    v-model="form.contractorName"
                    placeholder="请输入"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="编号：" prop="incidentCode">
                  <el-input
                    v-model="form.incidentCode"
                    placeholder="请输入"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="事件性质：" prop="incidentNature">
                  <el-select
                    v-model="form.incidentNature"
                    clearable
                    filterable
                    placeholder="请选择"
                    style="width: 100%"
                  >
                    <el-option label="限工事件" :value="1" />
                    <el-option label="医疗处置事件" :value="2" />
                    <el-option label="急救箱事件" :value="3" />
                    <el-option label="经济损失事件" :value="4" />
                    <el-option label="未遂事件" :value="5" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="发生时间：" prop="occurrenceTime">
                  <el-date-picker
                    v-model="form.occurrenceTime"
                    placeholder="选择日期"
                    style="width: 100%"
                    type="date"
                    value-format="yyyy-MM-dd"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="发生地点：" prop="occurrencePlace">
                  <el-input
                    v-model="form.occurrencePlace"
                    placeholder="请输入"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="经过描述：" prop="description">
                  <el-input
                    v-model="form.description"
                    placeholder="请输入"
                    :rows="4"
                    style="width: 100%"
                    type="textarea"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <div class="form-card">
            <div class="section-header">
              当事人信息
              <div class="section-actions">
                <el-button
                  icon="el-icon-plus"
                  size="small"
                  type="primary"
                  @click="addInvolvedPerson"
                >
                  新增
                </el-button>
              </div>
            </div>

            <div
              v-for="(person, index) in form.incidentPersonsInvolvedDtoList"
              :key="index"
              class="involved-person-section"
            >
              <div class="involved-person-header">
                <span>当事人 #{{ index + 1 }}</span>
                <el-button
                  v-if="form.incidentPersonsInvolvedDtoList.length > 1"
                  icon="el-icon-delete"
                  size="mini"
                  type="danger"
                  @click="removeInvolvedPerson(index)"
                >
                  删除
                </el-button>
              </div>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item
                    :label="'姓名：'"
                    :prop="'incidentPersonsInvolvedDtoList.' + index + '.name'"
                  >
                    <el-input
                      v-model="person.name"
                      placeholder="请输入姓名"
                      style="width: 100%"
                    />
                    <!-- <el-select
                      v-model="person.name"
                      clearable
                      filterable
                      placeholder="请选择"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in userList"
                        :key="item.userId"
                        :label="item.nickname"
                        :value="item.nickname"
                      />
                    </el-select> -->
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    :label="'性别：'"
                    :prop="
                      'incidentPersonsInvolvedDtoList.' + index + '.gender'
                    "
                  >
                    <el-radio-group v-model="person.gender" style="width: 100%">
                      <el-radio :label="1">男</el-radio>
                      <el-radio :label="2">女</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item
                    :label="'出生年月：'"
                    :prop="
                      'incidentPersonsInvolvedDtoList.' + index + '.birthDate'
                    "
                  >
                    <el-date-picker
                      v-model="person.birthDate"
                      placeholder="选择日期"
                      style="width: 100%"
                      type="date"
                      value-format="yyyy-MM-dd"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    :label="'用工性质：'"
                    :prop="
                      'incidentPersonsInvolvedDtoList.' +
                      index +
                      '.employmentNature'
                    "
                  >
                    <el-select
                      v-model="person.employmentNature"
                      clearable
                      filterable
                      placeholder="请选择"
                      style="width: 100%"
                    >
                      <el-option label="合同化用工" :value="1" />
                      <el-option label="市场化用工" :value="2" />
                      <el-option label="季节性临时工" :value="3" />
                      <el-option label="承包商员工" :value="4" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item
                    :label="'工种：'"
                    :prop="
                      'incidentPersonsInvolvedDtoList.' + index + '.position'
                    "
                  >
                    <el-input
                      v-model="person.position"
                      placeholder="请输入"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    :label="'从事目前工作年限：'"
                    :prop="
                      'incidentPersonsInvolvedDtoList.' +
                      index +
                      '.currentJobYears'
                    "
                  >
                    <el-input
                      v-model="person.currentJobYears"
                      placeholder="请输入"
                      style="width: 100%"
                      type="number"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item
                    :label="'伤害程度/部位：'"
                    :prop="
                      'incidentPersonsInvolvedDtoList.' +
                      index +
                      '.injuryDegree'
                    "
                  >
                    <el-input
                      v-model="person.injuryDegree"
                      placeholder="请输入"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    :label="'直接经济损失（元）：'"
                    :prop="
                      'incidentPersonsInvolvedDtoList.' +
                      index +
                      '.directEconomicLoss'
                    "
                  >
                    <el-input
                      v-model="person.directEconomicLoss"
                      placeholder="请输入"
                      style="width: 100%"
                      type="number"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>

          <div class="form-card">
            <div class="section-header">
              调查组成员信息
              <div class="section-actions">
                <el-button
                  icon="el-icon-plus"
                  size="small"
                  type="primary"
                  @click="addInvestigationTeamMember"
                >
                  新增
                </el-button>
              </div>
            </div>

            <el-table
              border
              :data="form.investigationTeamMembersDtoList"
              style="width: 100%; margin-bottom: 10px"
            >
              <el-table-column label="序号" type="index" width="50" />
              <el-table-column label="调查组成员" prop="personnelName">
                <template slot-scope="scope">
                  <!-- <el-input
                    v-model="scope.row.personnelName"
                    placeholder="请输入"
                    style="width: 100%"
                  /> -->
                  <el-select
                    v-model="scope.row.personnelId"
                    clearable
                    filterable
                    placeholder="请选择"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in userList"
                      :key="item.userId"
                      :label="item.nickname"
                      :value="item.userId"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="职务" prop="positionName">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.positionName"
                    placeholder="请输入"
                    style="width: 100%"
                  />
                </template>
              </el-table-column>
              <el-table-column label="单位" prop="unitName">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.unitName"
                    placeholder="请输入"
                    style="width: 100%"
                  />
                </template>
              </el-table-column>
              <el-table-column label="专业特长" prop="specialty">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.specialty"
                    placeholder="请输入"
                    style="width: 100%"
                  />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80">
                <template slot-scope="scope">
                  <el-button
                    circle
                    :disabled="form.investigationTeamMembersDtoList.length <= 1"
                    icon="el-icon-delete"
                    size="mini"
                    type="danger"
                    @click="removeInvestigationTeamMember(scope.$index)"
                  />
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div class="form-card">
            <div class="section-header">原因分析</div>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="行为原因：" prop="behaviorCause">
                  <el-input
                    v-model="form.behaviorCause"
                    placeholder="请输入"
                    :rows="3"
                    style="width: 100%"
                    type="textarea"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="物的原因：" prop="objectCause">
                  <el-input
                    v-model="form.objectCause"
                    placeholder="请输入"
                    :rows="3"
                    style="width: 100%"
                    type="textarea"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="环境原因：" prop="environmentCause">
                  <el-input
                    v-model="form.environmentCause"
                    placeholder="请输入"
                    :rows="3"
                    style="width: 100%"
                    type="textarea"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="管理原因：" prop="managementCause">
                  <el-input
                    v-model="form.managementCause"
                    placeholder="请输入"
                    :rows="3"
                    style="width: 100%"
                    type="textarea"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <div class="form-card">
            <div class="section-header">
              整改措施
              <div class="section-actions">
                <el-button
                  icon="el-icon-plus"
                  size="small"
                  type="primary"
                  @click="addCorrectiveMeasure"
                >
                  新增
                </el-button>
              </div>
            </div>

            <el-table
              border
              :data="form.correctiveMeasuresDtoList"
              style="width: 100%; margin-bottom: 10px"
            >
              <el-table-column label="序号" type="index" width="50" />
              <el-table-column label="纠正和预防措施" prop="measureDescription">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.measureDescription"
                    placeholder="请输入"
                    :rows="2"
                    style="width: 100%"
                    type="textarea"
                  />
                </template>
              </el-table-column>
              <el-table-column
                label="完成时间"
                prop="completionTime"
                width="180"
              >
                <template slot-scope="scope">
                  <el-date-picker
                    v-model="scope.row.completionTime"
                    placeholder="选择日期"
                    style="width: 100%"
                    type="date"
                    value-format="yyyy-MM-dd"
                  />
                </template>
              </el-table-column>
              <el-table-column
                label="责任人"
                prop="responsiblePersonName"
                width="180"
              >
                <template slot-scope="scope">
                  <el-select
                    v-model="scope.row.responsiblePersonId"
                    clearable
                    filterable
                    placeholder="请选择"
                    style="width: 100%"
                    @change="
                      handleResponsiblePersonChange(
                        scope.$index,
                        scope.row.responsiblePersonId
                      )
                    "
                  >
                    <el-option
                      v-for="item in userList"
                      :key="item.userId"
                      :label="item.nickname"
                      :value="item.userId"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80">
                <template slot-scope="scope">
                  <el-button
                    circle
                    :disabled="form.correctiveMeasuresDtoList.length <= 1"
                    icon="el-icon-delete"
                    size="mini"
                    type="danger"
                    @click="removeCorrectiveMeasure(scope.$index)"
                  />
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div class="form-card">
            <div class="section-header">审核信息</div>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item
                  label="纠正和预防措施验证结果："
                  prop="correctiveValidationResult"
                >
                  <el-input
                    v-model="form.correctiveValidationResult"
                    placeholder="请输入"
                    :rows="3"
                    style="width: 100%"
                    type="textarea"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="验证时间：" prop="validationTime">
                  <el-date-picker
                    v-model="form.validationTime"
                    placeholder="选择日期"
                    style="width: 100%"
                    type="date"
                    value-format="yyyy-MM-dd"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="验证人：" prop="validatorId">
                  <el-select
                    v-model="form.validatorId"
                    clearable
                    filterable
                    placeholder="请选择"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in userList"
                      :key="item.userId"
                      :label="item.nickname"
                      :value="item.userId"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item
                  label="属地主管（调查负责人）："
                  prop="localSupervisorId"
                >
                  <el-select
                    v-model="form.localSupervisorId"
                    clearable
                    filterable
                    placeholder="请选择"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in userList"
                      :key="item.userId"
                      :label="item.nickname"
                      :value="item.userId"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="时间：" prop="localSupervisorSignTime">
                  <el-date-picker
                    v-model="form.localSupervisorSignTime"
                    placeholder="选择日期"
                    style="width: 100%"
                    type="date"
                    value-format="yyyy-MM-dd"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer" style="text-align: center">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="save">暂 存</el-button>
        <el-button type="primary" @click="submitForm">提 交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { mapState } from 'vuex'
  import {
    // getEventReportingRelationById,
    createEventReportingRelation,
    submitEventReportingRelation,
    getEventReportingRelationList,
  } from '@/api/safety/eventReporting'

  export default {
    name: 'SafetyInvestigationForm',
    props: {
      visible: {
        type: Boolean,
        default: false,
      },
      eventData: {
        type: Object,
        default: () => ({}),
      },
    },
    data() {
      return {
        dialogVisible: false,
        form: {
          // 纠正和预防措施表
          correctiveMeasuresDtoList: [
            {
              measureDescription: undefined,
              completionTime: undefined,
              responsiblePersonId: undefined,
              responsiblePersonName: undefined,
            },
          ],
          // 事件当事人信息表
          incidentPersonsInvolvedDtoList: [
            {
              name: undefined,
              gender: undefined,
              birthDate: undefined,
              employmentNature: undefined,
              position: undefined,
              currentJobYears: undefined,
              injuryDegree: undefined,
              directEconomicLoss: undefined,
            },
          ],
          // 调查组成员信息表
          investigationTeamMembersDtoList: [
            {
              personnelId: undefined,
              personnelName: undefined,
              positionId: undefined,
              positionName: undefined,
              unitId: undefined,
              unitName: undefined,
              specialty: undefined,
            },
          ],
        },
        rules: {
          investigationDate: [
            { required: true, message: '请选择调查日期', trigger: 'change' },
          ],
          investigator: [
            { required: true, message: '请选择调查人', trigger: 'change' },
          ],
        },
      }
    },
    computed: {
      ...mapState({
        userList: (state) => state.user.userList,
      }),
    },
    watch: {
      visible(val) {
        this.dialogVisible = val
        if (val) {
          this.initFormData()
        }
      },
    },
    methods: {
      initFormData() {
        if (this.eventData) {
          this.getDatails(this.eventData.eventId)
        }
      },
      getDatails(id) {
        getEventReportingRelationList({ eventId: id })
          .then((res) => {
            this.form = res.data && res.data[0]
          })
          .finally(() => {
            this.form.id = id || '' // 绑定事件ID
          })
      },
      handleClose() {
        this.$refs['investigationForm'].resetFields()
        this.$emit('update:visible', false)
        this.$emit('close')
      },
      save() {
        createEventReportingRelation(this.form).then((res) => {
          if (res.code === 200) {
            this.$message.success('调查单暂存成功')
            this.handleClose()
          }
        })
      },
      submitForm() {
        this.$refs['investigationForm'].validate((valid) => {
          if (valid) {
            // 提交表单
            submitEventReportingRelation(this.form).then(() => {
              this.$message.success('调查单提交成功')
              this.handleClose()
            })
          } else {
            return false
          }
        })
      },
      // 当事人信息相关方法
      addInvolvedPerson() {
        this.form.incidentPersonsInvolvedDtoList.push({
          name: undefined,
          gender: 1,
          birthDate: undefined,
          employmentNature: undefined,
          position: undefined,
          currentJobYears: undefined,
          injuryDegree: undefined,
          directEconomicLoss: undefined,
        })
      },
      removeInvolvedPerson(index) {
        if (this.form.incidentPersonsInvolvedDtoList.length <= 1) {
          this.$message.warning('至少保留一条当事人信息')
          return
        }
        this.form.incidentPersonsInvolvedDtoList.splice(index, 1)
      },

      // 调查组成员信息相关方法
      addInvestigationTeamMember() {
        this.form.investigationTeamMembersDtoList.push({
          personnelId: undefined,
          personnelName: undefined,
          positionId: undefined,
          positionName: undefined,
          unitId: undefined,
          unitName: undefined,
          specialty: undefined,
        })
      },
      removeInvestigationTeamMember(index) {
        if (this.form.investigationTeamMembersDtoList.length <= 1) {
          this.$message.warning('至少保留一条调查组成员信息')
          return
        }
        this.form.investigationTeamMembersDtoList.splice(index, 1)
      },

      // 纠正和预防措施相关方法
      addCorrectiveMeasure() {
        this.form.correctiveMeasuresDtoList.push({
          measureDescription: undefined,
          completionTime: undefined,
          responsiblePersonId: undefined,
          responsiblePersonName: undefined,
        })
      },
      removeCorrectiveMeasure(index) {
        if (this.form.correctiveMeasuresDtoList.length <= 1) {
          this.$message.warning('至少保留一条纠正和预防措施')
          return
        }
        this.form.correctiveMeasuresDtoList.splice(index, 1)
      },
      handleResponsiblePersonChange(index, userId) {
        // 当选择责任人时，同时设置责任人姓名
        if (userId) {
          const user = this.userList.find((item) => item.userId === userId)
          if (user) {
            this.$set(
              this.form.correctiveMeasuresDtoList[index],
              'responsiblePersonName',
              user.nickname
            )
          }
        } else {
          this.$set(
            this.form.correctiveMeasuresDtoList[index],
            'responsiblePersonName',
            undefined
          )
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .investigation-form {
    padding: 0 10px;
  }

  .form-card {
    padding: 20px;
    margin-bottom: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  }

  .section-header {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 12px;
    margin: 0 0 16px;
    font-size: 16px;
    font-weight: 600;
    color: #111;
    border-left: 4px solid #409eff;
  }

  .section-actions {
    margin-right: 10px;
  }

  .involved-person-section {
    padding: 15px;
    margin-bottom: 15px;
    background-color: #f9f9f9;
    border: 1px solid #ebeef5;
    border-radius: 5px;
  }

  .involved-person-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 10px;
    margin-bottom: 15px;
    font-weight: 600;
    color: #303133;
    border-bottom: 1px dashed #dcdfe6;
  }

  ::v-deep {
    .el-form-item {
      margin-bottom: 22px;
    }

    .el-table .el-form-item.is-error .el-input__inner,
    .el-table .el-form-item.is-error .el-textarea__inner {
      border-color: #f56c6c;
    }

    .safety-investigation-dialog {
      position: absolute;
      top: 50%;
      left: 50%;
      display: flex;
      flex-direction: column;
      max-height: 90vh;
      margin-top: 0 !important;
      margin-bottom: 0 !important;
      transform: translate(-50%, -50%);

      .el-dialog__body {
        max-height: calc(90vh - 120px);
        overflow: auto;
      }
    }
  }
</style>
