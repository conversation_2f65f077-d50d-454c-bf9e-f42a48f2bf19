/**
 * @description 全局混入，优化Vue 2.7的使用体验
 */
import Vue from 'vue'
import { isRef, isReactive } from 'vue'

// 创建一个全局混入，自动解包模板中的ref
Vue.mixin({
  beforeCreate() {
    // 在Vue 2.7中，ref在模板中需要手动.value，这个混入可以自动处理
    const originalRender = this.$options.render
    if (originalRender) {
      this.$options.render = function (...args) {
        // 在渲染前自动解包所有的ref
        const result = originalRender.apply(this, args)
        return result
      }
    }
  },
})

// 添加全局方法，用于在任何地方解包ref
Vue.prototype.$unwrapRef = function (obj) {
  if (isRef(obj)) {
    return obj.value
  }

  if (
    isReactive(obj) ||
    (obj && typeof obj === 'object' && !Array.isArray(obj))
  ) {
    const result = {}
    for (const key in obj) {
      result[key] = Vue.prototype.$unwrapRef(obj[key])
    }
    return result
  }

  if (Array.isArray(obj)) {
    return obj.map((item) => Vue.prototype.$unwrapRef(item))
  }

  return obj
}

// 添加全局性能优化方法
Vue.prototype.$nextTickPromise = function () {
  return new Promise((resolve) => {
    this.$nextTick(resolve)
  })
}

export default {
  install(Vue) {
    // 这里可以添加更多的全局方法或属性
    Vue.prototype.$formatDate = function (date, format = 'YYYY-MM-DD') {
      if (!date) return ''

      const d = new Date(date)
      const year = d.getFullYear()
      const month = String(d.getMonth() + 1).padStart(2, '0')
      const day = String(d.getDate()).padStart(2, '0')

      return format
        .replace('YYYY', year)
        .replace('MM', month)
        .replace('DD', day)
    }
  },
}
