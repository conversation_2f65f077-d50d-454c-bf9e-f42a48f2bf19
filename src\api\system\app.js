import request from '@/utils/request'

export function getAppList(params) {
  return request({
    url: '/system/appVersion/list',
    method: 'get',
    params,
  })
}

export function postApp(data) {
  return request({
    url: '/system/appVersion',
    method: 'post',
    data,
  })
}

export function editApp(data) {
  return request({
    url: `/system/appVersion/update`,
    method: 'put',
    data,
  })
}

export function redoApp(id) {
  return request({
    url: `/system/appVersion/rePass?id=${id}`,
    method: 'post',
  })
}

export function delApp(params) {
  return request({
    url: `/system/appVersion/delete`,
    method: 'delete',
    params,
  })
}
