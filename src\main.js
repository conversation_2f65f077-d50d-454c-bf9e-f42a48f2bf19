import '@/vab'
import Vue from 'vue'
import App from './App'
import i18n from './i18n'
import router from './router'
import store from './store'
import ApprovalRecordDialog from '@/components/ApprovalRecordDialog'

/**
 * @description 正式环境默认使用mock，正式项目记得注释后再打包
 */
import { pwa } from './config'

// 导入Composition API工具函数
import './utils/compositionApi'

// 导入全局混入
import globalMixins from './utils/globalMixins'
Vue.use(globalMixins)

if (pwa) require('./registerServiceWorker')

// 设置生产环境提示
Vue.config.productionTip = false

// 注册全局组件
Vue.component('ApprovalRecordDialog', ApprovalRecordDialog)

// 创建Vue实例
new Vue({
  el: '#app',
  i18n,
  store,
  router,
  render: (h) => h(App),
})
