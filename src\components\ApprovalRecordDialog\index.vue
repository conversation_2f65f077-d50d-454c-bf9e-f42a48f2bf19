<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="800px"
    @close="handleClose"
  >
    <el-table v-loading="loading" border :data="recordList" style="width: 100%">
      <el-table-column align="center" label="审批人" prop="approverName">
        <template slot-scope="scope">
          <span v-for="(item, index) in scope.row.approver" :key="item.id">
            {{ index ? '、' + item.name : item.name }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="审批内容" prop="content" />
      <el-table-column align="center" label="审批时间" prop="approverTime" />
      <el-table-column align="center" label="审批状态" prop="approvalStatus">
        <template #default="scope">
          <el-tag v-if="scope.row.approvalStatus === 'IN_APPROVAL'">
            审批中
          </el-tag>
          <el-tag
            v-else-if="scope.row.approvalStatus === 'PASS'"
            type="success"
          >
            通过
          </el-tag>
          <el-tag
            v-else-if="scope.row.approvalStatus === 'REJECT'"
            type="danger"
          >
            驳回
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="备注"
        prop="reason"
        show-overflow-tooltip
      />
    </el-table>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关 闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
  export default {
    name: 'ApprovalRecordDialog',
    props: {
      visible: {
        type: Boolean,
        default: false,
      },
      title: {
        type: String,
        default: '审批记录',
      },
      recordList: {
        type: Array,
        default: () => [],
      },
      loading: {
        type: Boolean,
        default: false,
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.visible
        },
        set(val) {
          this.$emit('update:visible', val)
        },
      },
    },
    methods: {
      handleClose() {
        this.dialogVisible = false
        this.$emit('close')
      },
    },
  }
</script>
