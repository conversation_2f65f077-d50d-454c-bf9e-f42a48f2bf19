<template>
  <div>
    <div
      class="todo-container"
      style="background: #f7f9fb; border-radius: 16px"
    >
      <div>
        <div
          v-if="todoList.length !== 0"
          class="newBody"
          style="height: 290px; overflow-x: auto; overflow-y: hidden"
        >
          <el-table
            v-loading="loading"
            :data="todoList"
            :row-class-name="'rowstyle'"
            :show-header="false"
            :stripe="true"
            style="width: 100%"
            @row-click="goTo"
          >
            <el-table-column label="名称" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                【{{ scope.row.title }}】 {{ scope.row.projectName }}
              </template>
            </el-table-column>
            <el-table-column label="发起人" width="100">
              <template slot-scope="scope">
                {{ scope.row.initiatorPerson.name }}
              </template>
            </el-table-column>
            <el-table-column align="right" label="时间" width="170">
              <template slot-scope="scope">
                {{ scope.row.createdAt }}
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div
          v-else
          class="empty-container"
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 290px;
          "
        >
          <div
            class="text item"
            style="
              display: flex;
              align-items: center;
              justify-content: center;
              width: 100%;
              height: 100%;
            "
          >
            <el-image
              class="vab-data-emptys"
              :src="require('@/assets/empty_images/data_empty.png')"
              style="max-width: 200px; max-height: 200px"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 审核页面 -->
    <!-- <Process ref="processRef" @refresh="refreshData" /> -->
  </div>
</template>

<script>
  // import Process from './process.vue'
  export default {
    components: {
      // Process,
    },
    props: {
      loading: {
        type: Boolean,
        default: false,
      },
      todoList: {
        type: Array,
        default: () => [],
      },
    },
    methods: {
      goTo(item) {
        this.$refs.processRef.showEdit(item)
      },

      refreshData() {
        // 通知父组件刷新数据
        this.$emit('refresh')
      },

      moreTodo(type) {
        this.$router.push({
          name: 'MyAgentMatters',
          query: { type },
        })
      },
    },
  }
</script>

<style scoped>
  .newBody ::v-deep .rowstyle {
    cursor: pointer;
  }

  .noticeItem {
    cursor: pointer;
  }

  .doneTitle:hover {
    color: #ed691e;
  }

  .todo-container {
    position: relative;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  }

  .todo-badge {
    transform: scale(0.9);
  }
</style>
