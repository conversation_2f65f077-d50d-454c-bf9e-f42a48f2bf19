export const CODE_MESSAGE = {
  '00000': '一切ok',

  A0001: '用户端错误',
  A0200: '用户登录异常',

  A0201: '用户不存在',
  A0202: '用户账户被冻结',
  A0203: '用户账户已作废',

  A0210: '用户名或密码错误',
  A0211: '用户输入密码次数超限',
  A0212: '客户端认证失败',
  A0230: 'token无效或已过期',
  A0231: 'token已被禁止访问',

  A0300: '访问权限异常',
  A0301: '访问未授权',
  A0302: '演示环境禁止修改、删除重要数据，请本地部署后测试',

  A0400: '用户请求参数错误',
  A0401: '请求资源不存在',
  A0410: '请求必填参数为空',

  A0700: '用户上传文件异常',
  A0701: '用户上传文件类型不匹配',
  A0702: '用户上传文件太大',
  A0703: '用户上传图片太大',

  B0001: '系统执行出错',
  B0100: '系统执行超时',
  // B0100: '系统订单处理超时',

  B0200: '系统容灾功能被出发',
  B0210: '系统限流',
  B0220: '系统功能降级',

  B0300: '系统资源异常',
  B0310: '系统资源耗尽',
  B0320: '系统资源访问异常',
  B0321: '系统读取磁盘文件失败',

  C0001: '调用第三方服务出错',
  C0100: '中间件服务出错',
  C0113: '接口不存在',

  C0120: '消息服务出错',
  C0121: '消息投递出错',
  C0122: '消息消费出错',
  C0123: '消息订阅出错',
  C0124: '消息分组未查到',

  C0300: '数据库服务出错',
  C0311: '表不存在',
  C0312: '列不存在',
  C0321: '多表关联中存在多个相同名称的列',
  C0331: '数据库死锁',
  C0341: '主键冲突',
}
