<template>
  <div class="chart-container">
    <div ref="chartRef" class="progress-chart"></div>
    <div class="chart-data">
      <div class="data-item total-item">
        <div class="data-label">总量取数</div>
        <div class="data-value">{{ totalCount }}</div>
      </div>
      <div class="data-item">
        <div class="data-label">
          <span class="dot blue-dot"></span>
          已完成数
        </div>
        <div class="data-value">{{ completedCount }}</div>
      </div>
      <div class="data-item">
        <div class="data-label">
          <span class="dot orange-dot"></span>
          未完成数
        </div>
        <div class="data-value">{{ uncompletedCount }}</div>
      </div>
    </div>
  </div>
</template>

<script>
  import * as echarts from 'echarts'

  export default {
    name: 'QuestionProgress',
    props: {
      percentage: {
        type: Number,
        default: 50,
      },
      totalCount: {
        type: Number,
        default: 1381,
      },
      completedCount: {
        type: Number,
        default: 660,
      },
      uncompletedCount: {
        type: Number,
        default: 721,
      },
    },
    data() {
      return {
        chart: null,
      }
    },
    mounted() {
      this.$nextTick(() => {
        this.initChart()
      })
    },
    beforeDestroy() {
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    },
    methods: {
      initChart() {
        const chartDom = this.$refs.chartRef
        if (!chartDom) return

        this.chart = echarts.init(chartDom)
        const option = {
          series: [
            {
              type: 'gauge',
              startAngle: 180,
              endAngle: 0,
              center: ['50%', '75%'],
              radius: '100%',
              min: 0,
              max: 100,
              splitNumber: 0,
              axisLine: {
                lineStyle: {
                  width: 20,
                  color: [
                    [this.percentage / 100, '#4e7ae7'],
                    [1, '#fba242'],
                  ],
                },
              },
              pointer: {
                show: false,
              },
              axisTick: {
                show: false,
              },
              splitLine: {
                show: false,
              },
              axisLabel: {
                show: false,
              },
              detail: {
                show: true,
                offsetCenter: [0, '8%'],
                fontSize: 48,
                fontWeight: 'normal',
                formatter: `${this.percentage}%`,
                color: '#4e7ae7',
                backgroundColor: '#f5f7fa',
                borderRadius: 80,
                padding: [25, 40],
                rich: {
                  value: {
                    fontSize: 48,
                    fontWeight: 'normal',
                    color: '#4e7ae7',
                    padding: [0, 0, 0, 0],
                  },
                },
              },
              title: {
                show: true,
                offsetCenter: [0, '-15%'],
                fontSize: 14,
                color: '#666',
                formatter: '已完成',
                padding: [0, 0, 15, 0],
              },
              data: [
                {
                  value: this.percentage,
                  name: '已完成',
                },
              ],
            },
          ],
        }
        this.chart.setOption(option)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .chart-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 100%;
    padding: 20px;
  }

  .progress-chart {
    width: 60%;
    height: 250px;
  }

  .chart-data {
    display: flex;
    flex-direction: column;
    gap: 25px;
    width: 35%;
  }

  .data-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 5px 0;

    .data-label {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #666;

      .dot {
        display: inline-block;
        width: 8px;
        height: 8px;
        margin-right: 8px;
        border-radius: 50%;
      }

      .blue-dot {
        background-color: #4e7ae7;
      }

      .orange-dot {
        background-color: #fba242;
      }
    }

    .data-value {
      font-size: 18px;
      font-weight: bold;
      color: #333;
    }
  }

  .total-item {
    margin-bottom: 10px;
  }
</style>
