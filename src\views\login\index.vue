<template>
  <div class="login-container">
    <div
      v-if="onShowBrowser"
      style="
        float: right;
        padding: 15px 8px;
        font-size: 14px;
        font-weight: bold;
        color: #fff;
      "
    >
      为保证良好的使用效果，建议您使用谷歌浏览器访问
    </div>
    <el-row>
      <div class="login-header">HSE安全隐患系统</div>
      <div class="login-box">
        <!-- <div class="login-box-item">
          <img
            alt=""
            src="@/assets/login_images/login-form.png"
            style="height: inherit"
          />
        </div> -->
        <div class="login-box-item">
          <div class="login-form">
            <!-- <div class="ewm-box">
              <div
                v-if="!isEwm"
                class="ewm img"
                title="扫描二维码登录"
                @click="handleClickEwm"
              />
              <div v-if="isEwm" class="dn img" @click="handleClickEwm" />
            </div> -->

            <el-tabs
              v-if="!isEwm"
              v-model="activeName"
              @tab-click="handleClickTabs"
            >
              <el-tab-pane name="sms_code">
                <template slot="label">手机短信登录</template>
                <div
                  v-if="activeName == 'sms_code'"
                  style="width: 80%; margin: 10px auto"
                >
                  <el-form
                    ref="form"
                    :hide-required-asterisk="true"
                    label-position="left"
                    :model="smsform"
                    :rules="rules"
                  >
                    <el-form-item label="手机号" prop="mobile">
                      <el-input
                        v-model.trim="smsform.mobile"
                        v-focus
                        maxlength="11"
                        :placeholder="translateTitle('请输入手机号')"
                        size="mini"
                        type="text"
                      />
                    </el-form-item>
                    <el-form-item label="验证码" prop="code">
                      <el-input
                        v-model.trim="smsform.code"
                        :placeholder="translateTitle('短信验证码')"
                        type="text"
                      >
                        <template #suffix>
                          <el-button
                            :disabled="codeState"
                            type="primary"
                            @click="handleGetMessage"
                          >
                            {{ codeState ? `${time}s后重发` : '获取验证码' }}
                          </el-button>
                        </template>
                      </el-input>
                    </el-form-item>
                    <el-button
                      class="login-btn"
                      :loading="loading"
                      type="primary"
                      @click="handleLogin"
                    >
                      {{ translateTitle('登录') }}
                    </el-button>
                  </el-form>
                </div>
              </el-tab-pane>
              <el-tab-pane label="账号密码登录" name="captcha">
                <div
                  v-if="activeName == 'captcha'"
                  style="width: 80%; margin: 10px auto"
                >
                  <el-form
                    ref="form"
                    :hide-required-asterisk="true"
                    label-position="left"
                    :model="form"
                    :rules="rules"
                  >
                    <el-form-item label="用户名" prop="username">
                      <el-input
                        v-model.trim="form.username"
                        v-focus
                        :placeholder="translateTitle('请输入用户名')"
                        size="mini"
                        tabindex="1"
                        type="text"
                      />
                    </el-form-item>
                    <el-form-item label="密码" prop="password">
                      <el-input
                        :key="passwordType"
                        ref="password"
                        v-model.trim="form.password"
                        :placeholder="translateTitle('请输入密码')"
                        size="mini"
                        tabindex="2"
                        :type="passwordType"
                        @keyup.enter.native="handleLogin"
                      >
                        <template v-if="passwordType === 'password'" #suffix>
                          <vab-icon
                            class="show-password"
                            icon="eye-off-line"
                            @click="handlePassword"
                          />
                        </template>
                        <template v-else #suffix>
                          <vab-icon
                            class="show-password"
                            icon="eye-line"
                            @click="handlePassword"
                          />
                        </template>
                      </el-input>
                    </el-form-item>

                    <!-- 验证码验证逻辑需自行开发，如不需要验证码功能建议注释 -->
                    <el-form-item label="验证码" prop="code">
                      <el-input
                        v-model.trim="form.code"
                        :placeholder="translateTitle('验证码')"
                        tabindex="3"
                        type="text"
                      >
                        <template #suffix>
                          <el-image
                            class="code"
                            :src="codeUrl"
                            style="height: 32px"
                            @click="changeCode"
                          >
                            <div slot="error">获取失败</div>
                          </el-image>
                        </template>
                      </el-input>
                    </el-form-item>
                    <el-form-item style="margin-bottom: 0">
                      <div
                        style="
                          display: flex;
                          align-items: center;
                          justify-content: space-between;
                        "
                      >
                        <el-button style="color: unset" type="text">
                          忘记密码？
                        </el-button>
                        <el-button style="color: unset" type="text">
                          常见问题
                        </el-button>
                      </div>
                    </el-form-item>
                    <el-button
                      class="login-btn"
                      :loading="loading"
                      type="primary"
                      @click="handleLogin"
                    >
                      {{ translateTitle('登录') }}
                    </el-button>
                  </el-form>
                </div>
              </el-tab-pane>
            </el-tabs>
            <!-- <div v-if="isEwm" class="ewm-content">
              <div class="ewm-text">扫描登录</div>
              <div class="ewm-img">图片</div>
            </div> -->
          </div>
        </div>
      </div>
    </el-row>
  </div>
</template>

<script>
  import { translateTitle } from '@/utils/i18n'
  import { isPassword } from '@/utils/validate'
  import { mapActions, mapGetters } from 'vuex'
  // import QRCode from 'qrcodejs2'
  import { getCaptcha, getSmsCode } from '@/api/user'
  import { environment, onShowBrowserTips } from '@/config'

  export default {
    name: 'Login',
    directives: {
      focus: {
        inserted(el) {
          el.querySelector('input').focus()
        },
      },
    },
    beforeRouteLeave(to, from, next) {
      clearInterval(this.timer)
      next()
    },
    data() {
      const validateUsername = (rule, value, callback) => {
        if ('' === value)
          callback(new Error(this.translateTitle('用户名不能为空')))
        else callback()
      }
      const validatePassword = (rule, value, callback) => {
        if (!isPassword(value))
          callback(new Error(this.translateTitle('密码不能少于6位')))
        else callback()
      }
      return {
        activeName: 'sms_code',
        form: {
          username: '',
          password: '',
          code: '',
        },
        smsform: {
          mobile: '',
          code: '',
        },
        codeState: false,
        time: 60,
        rules: {
          username: [
            {
              required: true,
              trigger: 'blur',
              validator: validateUsername,
            },
          ],
          password: [
            {
              required: true,
              trigger: 'blur',
              validator: validatePassword,
            },
          ],
          code: [
            {
              required: true,
              trigger: 'blur',
              message: '验证码不能空',
            },
          ],
          mobile: [
            {
              required: true,
              pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
              message: '请输入正确的手机号码',
              trigger: 'blur',
            },
          ],
        },
        loading: false,
        passwordType: 'password',
        redirect: undefined,
        timer: 0,
        codeUrl: '',
        isEwm: false,
        onShowBrowser: false,
      }
    },
    computed: {
      ...mapGetters({
        title: 'settings/title',
      }),
    },
    watch: {
      $route: {
        handler(route) {
          this.redirect = (route.query && route.query.redirect) || '/'
        },
        immediate: true,
      },
    },
    created() {},
    mounted() {
      this.onShowBrowser = onShowBrowserTips ? true : false
      if (environment == 'development') {
        this.form.username = 'admin'
        this.form.password = '123456'
      }
      // this.changeCode()
    },
    methods: {
      ...mapActions({
        login: 'user/login',
      }),
      translateTitle,
      handlePassword() {
        this.passwordType === 'password'
          ? (this.passwordType = '')
          : (this.passwordType = 'password')
        this.$nextTick(() => {
          this.$refs.password.focus()
        })
      },
      handleRoute() {
        return this.redirect === '/404' || this.redirect === '/403'
          ? '/'
          : this.redirect
      },
      handleLogin() {
        this.$refs.form.validate(async (valid) => {
          if (valid)
            try {
              this.loading = true
              let paramsform = {
                grant_type: this.activeName,
              }
              if (this.activeName == 'captcha') {
                paramsform = Object.assign(paramsform, this.form)
              }
              if (this.activeName == 'sms_code') {
                paramsform = Object.assign(paramsform, this.smsform)
              }
              await this.login(paramsform).catch(() => {
                if (this.activeName == 'captcha') this.changeCode()
              })
              await this.$router.push(this.handleRoute())
            } finally {
              this.loading = false
            }
        })
      },
      async changeCode() {
        //切换验证码
        const {
          data: {
            data: { img, uuid },
          },
        } = await getCaptcha()
        this.codeUrl = 'data:image/gif;base64,' + img
        this.form.uuid = uuid
      },
      async handleGetMessage() {
        if (!this.codeState) {
          this.$refs.form.validateField('mobile', async (valid) => {
            if (!valid) {
              //发送请求
              let params = {
                phoneNumber: this.smsform.mobile,
              }
              const {
                data: { code, msg },
              } = await getSmsCode(params)
              if (code == '00000') {
                this.$baseMessage('验证码已发送，5分钟内有效！', 'success')
                this.codeState = true
                let timeout = setInterval(() => {
                  this.time--
                  if (this.time == 0) {
                    clearInterval(timeout)
                    this.time = 60
                    this.codeState = false
                  }
                }, 1000)
              } else {
                this.$baseMessage(msg, 'error')
              }
            }
          })
        }
      },
      handleClickTabs() {
        if (this.activeName == 'captcha') {
          this.changeCode()
        }
        if (this.activeName == 'sms_code') {
          this.codeState = false
          this.time = 60
        }
        this.$refs.form.resetFields()
      },
      handleClickEwm() {
        // this.isEwm = !this.isEwm
      },
    },
  }
</script>

<style lang="scss" scoped>
  .login-container {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background-image: url('~@/assets/login_images/background.jpg');
    background-repeat: no-repeat;
    background-position: 50%;
    background-size: cover;
  }
  .login-container::before {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: inherit;
    content: '';
    background: rgba(59, 88, 120, 0.4);
  }

  .login-header {
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin-top: 200px;
    font-size: 26px;
    color: white;
    .login-header-item {
      margin-top: 25px;
    }
  }
  .login-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 60px;
    background: white;
    .text {
      float: right;
      margin-top: -20px;
      margin-right: 200px;
      color: white;
    }
  }
  .login-box {
    display: flex;
    width: fit-content;
    margin: calc((100vh - 425px) / 2) auto 5vw;
    margin-top: 30px;
    border-radius: 4px;
    .login-box-item {
      height: 400px;
    }
  }
  .login-form {
    position: relative;
    width: 350px;
    height: inherit;
    overflow: hidden;
    background-color: white;
    border-radius: 2px;
    .title {
      font-size: 54px;
      font-weight: 500;
      color: #fff;
    }

    .title-tips {
      padding: 1vh 4.5vh;
      margin-top: 20px;
      font-size: 26px;
      font-weight: 400;
      color: rgba(92, 102, 240, 1);
    }

    .login-btn {
      display: inherit;
      width: 100%;
      background-color: #fba242;
      border: 0;

      &:hover {
        opacity: 0.9;
      }
    }

    .tips {
      margin-bottom: 10px;
      font-size: $base-font-size-default;
      color: $base-color-white;

      span {
        &:first-of-type {
          margin-right: 16px;
        }
      }
    }

    .title-container {
      position: relative;

      .title {
        margin: 0 auto 40px auto;
        font-size: 34px;
        font-weight: bold;
        color: $base-color-blue;
        text-align: center;
      }
    }

    i {
      position: absolute;
      top: 8px;
      left: 5px;
      z-index: $base-z-index;
      font-size: 16px;
      color: #d7dee3;
      cursor: pointer;
      user-select: none;
    }

    .show-password {
      position: absolute;
      top: unset;
      right: 25px;
      left: -35px;
      font-size: 16px;
      color: #d7dee3;
      cursor: pointer;
      user-select: none;
    }

    .ewm-content {
      width: 70%;
      height: 330px;
      padding: 50px;
      margin: auto;
      text-align: center;
      .ewm-text {
        font-weight: 600;
        color: #0d3f84;
      }
      .ewm-img {
        width: 150px;
        height: 150px;
        margin: auto;
        margin-top: 30px;
        border: 1px solid #999;
      }
    }
    ::v-deep {
      .el-form-item {
        padding-right: 0;
        margin-bottom: 10px;
        color: #454545;
        background: transparent;
        border: 1px solid transparent;
        border-radius: 2px;
        &__label {
          line-height: 20px;
        }
        &__content {
          min-height: $base-input-height;
          line-height: $base-input-height;
        }

        &__error {
          position: absolute;
          top: 100%;
          left: 18px;
          font-size: $base-font-size-small;
          color: $base-color-red;
        }
      }

      .el-input {
        box-sizing: border-box;
      }

      .el-tabs {
        &__nav {
          width: 100%;
        }
        &__item {
          width: 50%;
          text-align: center;
        }
        &__item.is-active {
          color: white;
          background-color: #4e8cc4;
        }
      }
      .el-input__suffix {
        right: 0;
      }
      .ewm-box {
        position: absolute;
        top: 0;
        right: 0;
        z-index: 999;
        border: 20px solid transparent;
        border-top-color: white;
        border-right-color: white;
        .img {
          position: absolute;
          top: -15px;
          right: -16px;
          width: 25px;
          height: 25px;
          background-repeat: no-repeat;
          background-position: 100%;
          background-size: cover;
        }
        .ewm {
          background-image: url('~@/assets/login_images/ewm.png');
        }
        .dn {
          background-image: url('~@/assets/login_images/dn.png');
        }
      }
    }
  }

  .qr_ins {
    display: flex;
    align-items: center;
    float: left;
    width: 50%;
    height: 100%;
  }

  .qr_ins .android,
  .qr_ins .ios {
    display: flex;
    flex-direction: row;
    padding: 10px 0;
  }

  .android-header > p {
    width: 100%;
    margin: 0;
    text-align: 20px;
  }
  .welcome-tips {
    width: 80%;
    margin: auto;
    .el-divider {
      background-color: #0d3f84;
    }
    ::v-deep {
      .el-divider__text {
        width: max-content;
        font-size: 14px;
        font-weight: 600;
        text-decoration: underline;
        text-underline-position: under;
        a {
          color: #0d3f84;
        }
      }
    }
  }
</style>
