<template>
  <div>
    <el-drawer
      ref="drawer"
      :before-close="close"
      :close-on-click-modal="false"
      custom-class="event-report-drawer"
      direction="rtl"
      size="100%"
      :title="title + '生产安全事故报告单'"
      :visible.sync="dialogFormVisible"
      @close="close"
    >
      <div class="drawer-container">
        <div class="drawer-content">
          <el-form
            ref="form"
            label-width="150px"
            :model="form"
            :rules="rules"
            style="height: 100%"
          >
            <div class="form-card">
              <div class="section-header">基本信息</div>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="填表时间：" prop="reportTime">
                    <el-date-picker
                      v-model="form.reportTime"
                      placeholder="请选择"
                      style="width: 100%"
                      type="datetime"
                      value-format="yyyy-MM-dd HH:mm:ss"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="事故单位：" prop="accidentUnitName">
                    <el-input
                      v-model="form.accidentUnitName"
                      placeholder="请输入"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="事故类型：" prop="accidentType">
                    <!-- <el-input
                      v-model="form.accidentType"
                      placeholder="请输入"
                      style="width: 100%"
                    /> -->
                    <el-select
                      v-model="form.accidentType"
                      clearable
                      filterable
                      placeholder="请选择"
                      style="width: 100%"
                    >
                      <el-option label="工业生产安全事故" :value="1" />
                      <el-option label="道路交通事故" :value="2" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="事故时间：" prop="accidentTime">
                    <el-date-picker
                      v-model="form.accidentTime"
                      placeholder="请选择"
                      style="width: 100%"
                      type="datetime"
                      value-format="yyyy-MM-dd HH:mm:ss"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row v-if="form.accidentType === 1" :gutter="20">
                <el-col :span="12">
                  <el-form-item label="类别细分：" prop="categorySegmentation">
                    <el-select
                      v-model="form.categorySegmentation"
                      clearable
                      filterable
                      placeholder="请选择"
                      style="width: 100%"
                    >
                      <!-- 灼烫、泄露、塌陷、高处坠落、滑倒滑落、割刺擦伤、触电、起重伤害、机械伤害、物体打击、健康伤害、其他伤害 -->
                      <el-option label="灼烫" :value="1" />
                      <el-option label="泄露" :value="2" />
                      <el-option label="塌陷" :value="3" />
                      <el-option label="高处坠落" :value="4" />
                      <el-option label="滑倒滑落" :value="5" />
                      <el-option label="割刺擦伤" :value="6" />
                      <el-option label="触电" :value="7" />
                      <el-option label="起重伤害" :value="8" />
                      <el-option label="机械伤害" :value="9" />
                      <el-option label="物体打击" :value="10" />
                      <el-option label="健康伤害" :value="11" />
                      <el-option label="其他伤害" :value="12" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item
                    label="事故地点及现场情况："
                    prop="locationDetails"
                  >
                    <el-input
                      v-model="form.locationDetails"
                      placeholder="请输入"
                      :rows="4"
                      style="width: 100%"
                      type="textarea"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
            <div class="form-card">
              <div class="section-header">伤损信息</div>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="死亡：" prop="deaths">
                    <el-input
                      v-model="form.deaths"
                      placeholder="请输入"
                      style="width: 100%"
                    >
                      <template slot="append">人</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="重伤：" prop="seriousInjuries">
                    <el-input
                      v-model="form.seriousInjuries"
                      placeholder="请输入"
                      style="width: 100%"
                    >
                      <template slot="append">人</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="轻伤：" prop="minorInjuries">
                    <el-input
                      v-model="form.minorInjuries"
                      placeholder="请输入"
                      style="width: 100%"
                    >
                      <template slot="append">人</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="失踪：" prop="missing">
                    <el-input
                      v-model="form.missing"
                      placeholder="请输入"
                      style="width: 100%"
                    >
                      <template slot="append">人</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="医疗：" prop="medical">
                    <el-input
                      v-model="form.medical"
                      placeholder="请输入"
                      style="width: 100%"
                    >
                      <template slot="append">人</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="限工：" prop="restrictedWork">
                    <el-input
                      v-model="form.restrictedWork"
                      placeholder="请输入"
                      style="width: 100%"
                    >
                      <template slot="append">人</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="初步直接经济损失：" prop="directLoss">
                    <el-input
                      v-model="form.directLoss"
                      placeholder="请输入"
                      style="width: 100%"
                    >
                      <template slot="append">元</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
            <div class="form-card">
              <div class="section-header">其它信息</div>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="事故简要经过：" prop="accidentSummary">
                    <el-input
                      v-model="form.accidentSummary"
                      placeholder="请输入"
                      :rows="4"
                      style="width: 100%"
                      type="textarea"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="采取的措施及媒体舆情：" prop="measures">
                    <el-input
                      v-model="form.measures"
                      placeholder="请输入"
                      :rows="4"
                      style="width: 100%"
                      type="textarea"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="初步原因分析：" prop="causeAnalysis">
                    <el-input
                      v-model="form.causeAnalysis"
                      placeholder="请输入"
                      :rows="4"
                      style="width: 100%"
                      type="textarea"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="填表人：" prop="reporterId">
                    <el-select
                      v-model="form.reporterId"
                      clearable
                      filterable
                      placeholder="请选择"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in storeUserList"
                        :key="item.userId"
                        :label="item.nickname"
                        :value="item.userId"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="负责人：" prop="responsiblePersonId">
                    <el-select
                      v-model="form.responsiblePersonId"
                      clearable
                      filterable
                      placeholder="请选择"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in storeUserList"
                        :key="item.userId"
                        :label="item.nickname"
                        :value="item.userId"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="联系电话：" prop="contactPhone">
                    <el-input
                      v-model="form.contactPhone"
                      placeholder="请输入"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12" />
              </el-row>
            </div>
          </el-form>
        </div>
        <div class="drawer-footer">
          <el-button size="medium" @click="close">取 消</el-button>
          <el-button size="medium" type="primary" @click="submitForm">
            暂 存
          </el-button>
          <el-button size="medium" type="primary" @click="save">
            提 交
          </el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script>
  import { mapState } from 'vuex'
  import {
    getAccidentReportingById,
    saveAccidentReporting,
    addAccidentReporting,
  } from '@/api/safety/accidentReporting'
  export default {
    name: '',
    data() {
      return {
        form: {},
        content: '',
        rules: {
          templateName: [
            { required: true, trigger: 'blur', message: '内容不能为空' },
          ],
        },
        title: '',
        dialogFormVisible: false,
      }
    },
    computed: {
      ...mapState({
        userInfo: (state) => state.user.user,
        storeUserList: (state) => state.user.userList,
      }),
    },
    methods: {
      showEdit(row) {
        if (!row) {
          this.title = '添加'
        } else {
          this.title = '编辑'
          // this.form = row
          this.getDetails(row.id)
        }
        this.dialogFormVisible = true
      },
      getDetails(id) {
        getAccidentReportingById(id).then((res) => {
          this.form = res.data
        })
      },
      close() {
        this.$refs['form'].resetFields()
        this.form = this.$options.data().form
        this.dialogFormVisible = false
      },

      submitForm() {
        saveAccidentReporting(this.form).then((res) => {
          if (res.code === 200) {
            this.$message.success('暂存成功')
            this.close()
          }
        })
      },

      save() {
        this.$refs['form'].validate((valid) => {
          if (valid) {
            // 提交表单
            addAccidentReporting(this.form).then(() => {
              this.$message.success('提交成功')
              this.close()
            })
          } else {
            return false
          }
        })
      },
    },
  }
</script>
<style lang="scss" scoped>
  .drawer-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #f5f7fa;
  }

  .drawer-content {
    height: calc(100% - 69px);
    padding: 20px;
    padding-top: 10px;
    overflow-y: auto;

    .section-header {
      position: relative;
      padding-left: 12px;
      margin: 24px 0 16px;
      font-size: 16px;
      font-weight: 600;
      color: #111;
      border-left: 4px solid #409eff;

      &:before {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        width: 4px;
        content: '';
        background-color: #409eff;
        border-radius: 2px;
      }
    }

    .form-card {
      padding: 20px;
      margin-bottom: 20px;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    }
  }

  .detail-section {
    margin-top: 24px;
  }

  .drawer-footer {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 10;
    padding: 16px;
    text-align: center;
    background: #fff;
    border-top: 1px solid #e8e8e8;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);

    .el-button {
      padding: 10px 24px;
      margin-left: 12px;
      font-weight: 500;
      border-radius: 4px;

      &:first-child {
        margin-left: 0;
      }
    }
  }

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .injured-person-section {
    position: relative;
    padding: 10px 0;

    .injured-person-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-bottom: 10px;
      margin-bottom: 15px;
      border-bottom: 1px dashed #e0e0e0;

      span {
        font-size: 16px;
        font-weight: 500;
        color: #409eff;
      }
    }
  }

  // 调整表单样式
  ::v-deep {
    // 抽屉样式优化
    .event-report-drawer {
      .el-drawer__header {
        padding: 16px 20px;
        margin-bottom: 0;
        font-size: 18px;
        font-weight: 600;
        text-align: center;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

        .el-drawer__close-btn {
          font-size: 20px;
          color: #555;

          &:hover {
            color: #000;
          }
        }
      }

      .el-drawer__body {
        padding: 0;
      }
    }

    // 表单项样式
    .el-form-item {
      margin-bottom: 24px;

      &__label {
        font-weight: 500;
        color: #606266;
      }

      &__error {
        padding-top: 4px;
      }
    }

    // 输入框样式
    .el-input,
    .el-textarea {
      .el-input__inner,
      .el-textarea__inner {
        border-color: #dcdfe6;
        border-radius: 4px;
        transition: all 0.3s;

        &:hover {
          border-color: #c0c4cc;
        }

        &:focus {
          border-color: #409eff;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }
      }
    }

    // 日期选择器样式
    .el-date-editor {
      width: 100% !important;

      .el-input__prefix {
        color: #909399;
      }
    }

    // 下拉选择器样式
    .el-select {
      width: 100%;

      .el-input__inner {
        &:hover {
          border-color: #c0c4cc;
        }

        &:focus {
          border-color: #409eff;
        }
      }
    }

    // 单选按钮样式
    .el-radio {
      margin-right: 20px;

      &__inner {
        &::after {
          width: 6px;
          height: 6px;
        }
      }

      &__input.is-checked + .el-radio__label {
        color: #409eff;
      }
    }

    // 数字输入框样式
    .el-input-number {
      width: 100%;

      .el-input-number__decrease,
      .el-input-number__increase {
        background-color: #f5f7fa;
        border-color: #dcdfe6;

        &:hover {
          color: #409eff;
        }
      }
    }

    // 表格样式
    .el-table {
      margin-bottom: 60px;
      overflow: hidden;
      border-radius: 8px;

      th {
        font-weight: 600;
        color: #606266;
        background-color: #f5f7fa;
      }
    }

    // 按钮样式
    .el-button {
      &--primary {
        background-color: #409eff;
        border-color: #409eff;

        &:hover,
        &:focus {
          background-color: #66b1ff;
          border-color: #66b1ff;
        }
      }
    }
  }
</style>
