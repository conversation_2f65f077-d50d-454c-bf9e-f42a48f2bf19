<!-- 事件上报 -->
<template>
  <div class="event-reporting">
    <!-- 查询区域 -->
    <el-form ref="queryFormRef" :inline="true" :model="queryParams">
      <el-form-item>
        <el-button icon="el-icon-plus" type="success" @click="handleAdd">
          新增
        </el-button>
        <el-button
          :disabled="single"
          icon="el-icon-delete"
          type="danger"
          @click="handleDelete"
        >
          删除
        </el-button>
      </el-form-item>

      <!-- <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          clearable
          placeholder="请输入名称"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          class="filter-item"
          icon="el-icon-search"
          type="primary"
          @click="handleQuery"
        >
          搜索
        </el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </el-form-item> -->
    </el-form>

    <!-- 表格 -->
    <el-table
      v-loading="loading"
      :data="tableData"
      default-expand-all
      row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column align="center" label="事故编号" prop="accidentNo" />
      <el-table-column align="center" label="事故级别" prop="accidentLevel" />
      <el-table-column
        align="center"
        label="单位/项目名称"
        prop="accidentUnitName"
      />
      <el-table-column align="center" label="发生地点" prop="locationDetails" />
      <el-table-column align="center" label="报告人" prop="reporterName" />
      <el-table-column align="center" label="发生时间" prop="accidentTime" />
      <el-table-column align="center" label="操作" width="140">
        <template #default="scope">
          <el-button
            circle
            icon="el-icon-edit-outline"
            plain
            type="primary"
            @click.stop="handleUpdate(scope.row)"
          />
          <el-button
            circle
            icon="el-icon-delete"
            plain
            type="danger"
            @click.stop="handleDelete(scope.row)"
          />
          <el-tooltip
            class="item"
            content="变更项目阶段状态"
            effect="dark"
            placement="top"
          >
            <el-button
              circle
              icon="el-icon-refresh"
              plain
              type="primary"
              @click.stop="changeApproval(scope.row)"
            />
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页工具条 -->
    <el-pagination
      v-if="total > 0"
      background
      :current-page="queryParams.pageNum"
      :layout="layout"
      :page-size="queryParams.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <!-- 详情 -->
    <Details ref="details" @refreshDataList="getList" />
  </div>
</template>

<script>
  import Details from './details.vue'
  import { getAccidentReportingList } from '@/api/safety/accidentReporting'

  export default {
    name: 'EventReporting',
    components: {
      Details,
    },
    data() {
      return {
        queryParams: {
          pageNum: 1,
          pageSize: 10,
        },
        total: 0,
        dataList: [],
        tableData: [], // 用于存储处理后的表格数据
        loading: false,
        selectionList: [],
        layout: 'total, sizes, prev, pager, next, jumper',
        single: true,
      }
    },
    mounted() {
      this.fetchData()
    },
    methods: {
      async getList() {
        const { data, page } = await getAccidentReportingList(this.queryParams)
        this.tableData = data
        this.total = page.totalCount
        this.loading = false
      },
      handleQuery() {
        this.queryParams.pageNum = 1
        this.fetchData()
      },
      resetQuery() {
        this.queryParams = {}
        this.handleQuery()
      },
      handleSizeChange(val) {
        this.queryParams.pageSize = val
        this.fetchData()
      },
      handleCurrentChange(val) {
        this.queryParams.pageNum = val
        this.fetchData()
      },
      fetchData() {
        this.loading = true
        this.getList()
      },
      handleAdd() {
        this.$refs.details.showEdit()
      },
      handleUpdate(row) {
        this.$refs.details.showEdit(row)
      },
    },
  }
</script>
