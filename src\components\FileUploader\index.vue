<template>
  <div class="file-uploader">
    <el-upload
      :accept="accept"
      :action="uploadUrl"
      :before-upload="beforeUpload"
      class="uploader"
      :disabled="disabled"
      :drag="drag"
      :file-list="fileList"
      :headers="headerObj"
      :limit="limit"
      :list-type="listType"
      :multiple="multiple"
      :on-error="handleError"
      :on-exceed="handleExceed"
      :on-preview="handlePreview"
      :on-progress="handleProgress"
      :on-remove="handleRemove"
      :on-success="handleSuccess"
    >
      <template v-if="drag">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
      </template>
      <el-button v-else size="small" type="primary">{{ buttonText }}</el-button>
      <div v-if="showTip" class="el-upload__tip" slot="tip">
        只能上传{{
          accept.replace(/\./g, '').replace(/,/g, '/')
        }}文件，且不超过{{ maxSize }}MB
      </div>
      <div v-if="showProgress && uploadProgress > 0" class="upload-progress">
        <el-progress :percentage="uploadProgress" :stroke-width="5" />
      </div>
    </el-upload>
  </div>
</template>

<script>
  import { getToken } from '@/utils/token'
  import { baseURL, buketName } from '@/config'
  import moment from 'moment'

  export default {
    name: 'FileUploader',
    props: {
      value: {
        type: Array,
        default: () => [],
      },
      maxSize: {
        type: Number,
        default: 200,
      },
      multiple: {
        type: Boolean,
        default: true,
      },
      limit: {
        type: Number,
        default: 10,
      },
      accept: {
        type: String,
        default: '.jpg,.jpeg,.png,.pdf,.doc,.docx,.xls,.xlsx,.mp4,',
      },
      buttonText: {
        type: String,
        default: '点击上传',
      },
      subPath: {
        type: String,
        default: 'perform-file',
      },
      drag: {
        type: Boolean,
        default: false,
      },
      showTip: {
        type: Boolean,
        default: true,
      },
      showProgress: {
        type: Boolean,
        default: false,
      },
      disabled: {
        type: Boolean,
        default: false,
      },
      listType: {
        type: String,
        default: 'text',
      },
      compressOptions: {
        type: Object,
        default: () => ({
          maxWidth: 1920,
          maxHeight: 1080,
          quality: 0.8,
        }),
      },
      showFileInfo: {
        type: Boolean,
        default: true,
      },
    },
    data() {
      return {
        fileList: [],
        uploadedFiles: [],
        fileInfoList: [], // 新增：存储文件信息（类型和大小）
        headerObj: {
          Authorization: `${getToken()}`,
        },
        uploadUrl: `${baseURL}swisp-base-service/api/v1/oss/ali/upload?subPath=${
          this.subPath
        }/${moment().format('YYYY/MM/DD')}&buketName=${buketName}`,
        uploadProgress: 0,
      }
    },
    watch: {
      value: {
        handler(val) {
          if (val && val.length > 0) {
            this.initFileList(val)
          }
        },
        immediate: true,
      },
    },
    methods: {
      // 获取文件扩展名
      getFileExtension(filename) {
        if (!filename) return ''
        const ext = '.' + filename.split('.').pop().toLowerCase()
        return ext
      },

      // 获取文件扩展名（不带点号）
      getFileExtensionWithoutDot(filename) {
        if (!filename) return ''
        return filename.split('.').pop().toLowerCase()
      },

      initFileList(files) {
        // 处理上传文件列表
        this.uploadedFiles = files.map((item) => {
          const fileType = this.getFileExtensionWithoutDot(item.name)
          // 如果已有size和fileType则使用，否则添加
          return {
            ...item,
            platform: item.platform || fileType,
            size: item.size || 0,
            fileSizeFormatted:
              item.fileSizeFormatted || this.formatFileSize(item.size || 0),
          }
        })

        // 处理显示文件列表
        this.fileList = files.map((item) => ({
          name: item.name,
          url: item.url,
        }))

        // 初始化文件信息列表
        this.fileInfoList = files.map((item) => {
          const fileType =
            item.platform || this.getFileExtensionWithoutDot(item.name)
          return {
            name: item.name,
            url: item.url,
            platform: fileType,
            fileSize:
              item.fileSizeFormatted ||
              (item.size ? this.formatFileSize(item.size) : '未知大小'),
          }
        })
      },

      // 格式化文件大小
      formatFileSize(size) {
        if (!size) return '未知大小'

        const units = ['B', 'KB', 'MB', 'GB']
        let fileSize = size
        let unitIndex = 0

        while (fileSize > 1024 && unitIndex < units.length - 1) {
          fileSize = fileSize / 1024
          unitIndex++
        }

        return `${fileSize.toFixed(2)} ${units[unitIndex]}`
      },

      // 根据文件类型获取对应的图标
      getFileIcon(fileType) {
        const iconMap = {
          '.jpg': 'el-icon-picture',
          '.jpeg': 'el-icon-picture',
          '.png': 'el-icon-picture',
          '.pdf': 'el-icon-document',
          '.doc': 'el-icon-document',
          '.docx': 'el-icon-document',
          '.xls': 'el-icon-document',
          '.xlsx': 'el-icon-document',
          '.mp4': 'el-icon-video-camera',
        }

        return iconMap[fileType] || 'el-icon-document'
      },

      async beforeUpload(file) {
        // 检查文件大小
        const isLt20M = file.size / 1024 / 1024 < this.maxSize
        if (!isLt20M) {
          this.$message.error(`上传文件大小不能超过 ${this.maxSize}MB!`)
          return false
        }

        // 检查文件类型
        if (this.accept) {
          const fileExtension = '.' + file.name.split('.').pop().toLowerCase()
          const acceptList = this.accept.split(',')
          const isAccept = acceptList.includes(fileExtension)
          if (!isAccept) {
            this.$message.error(
              `只能上传${this.accept
                .replace(/\./g, '')
                .replace(/,/g, '/')}格式的文件!`
            )
            return false
          }
        }

        return true
      },

      handleSuccess(res, file) {
        this.uploadProgress = 0
        const fileExtension = this.getFileExtensionWithoutDot(file.name)
        const fileSize = file.size

        const fileInfo = {
          name: file.name,
          url: res.data.path,
          // url: res.data.file,
          // platform: file.raw ? file.raw.type : file.type,
          size: fileSize, // 保存原始大小（字节）
          platform: fileExtension, // 保存文件类型（jpg等格式，不带点号）
          fileSizeFormatted: this.formatFileSize(fileSize), // 添加格式化后的文件大小
        }

        this.uploadedFiles.push(fileInfo)
        this.fileList.push({
          name: file.name,
          url: res.data.file,
        })

        // 添加到文件信息列表
        this.fileInfoList.push({
          name: file.name,
          url: res.data.file,
          platform: fileExtension,
          fileSize: this.formatFileSize(fileSize),
        })

        this.$emit('input', this.uploadedFiles)
        this.$emit('on-success', fileInfo)
      },

      handleRemove(file) {
        const fileIndex = this.fileList.findIndex(
          (item) => item.name === file.name && item.url === file.url
        )
        if (fileIndex > -1) {
          this.fileList.splice(fileIndex, 1)
        }

        const uploadedIndex = this.uploadedFiles.findIndex(
          (item) => item.name === file.name && item.url === file.url
        )
        if (uploadedIndex > -1) {
          this.uploadedFiles.splice(uploadedIndex, 1)
        }

        // 从文件信息列表中移除
        const infoIndex = this.fileInfoList.findIndex(
          (item) => item.name === file.name && item.url === file.url
        )
        if (infoIndex > -1) {
          this.fileInfoList.splice(infoIndex, 1)
        }

        this.$emit('input', this.uploadedFiles)
        this.$emit('on-remove', file)
      },

      handlePreview(file) {
        window.open(file.url, '_blank')
      },

      handleError(err, file) {
        this.uploadProgress = 0
        this.$emit('on-error', err, file)
      },

      handleExceed(files, fileList) {
        this.$message.warning(
          `当前限制选择 ${this.limit} 个文件，本次选择了 ${
            files.length
          } 个文件，共选择了 ${files.length + fileList.length} 个文件`
        )
        this.$emit('on-exceed', files, fileList)
      },

      handleProgress(event, file) {
        this.uploadProgress = Math.floor(event.percent)
        this.$emit('on-progress', event, file)
      },

      reset() {
        this.fileList = []
        this.uploadedFiles = []
        this.fileInfoList = []
        this.uploadProgress = 0
      },
    },
  }
</script>

<style lang="scss" scoped>
  .file-uploader {
    width: 100%;

    .upload-progress {
      margin-top: 10px;
    }

    .uploader {
      width: 100%;
    }

    .file-info-list {
      margin-top: 10px;
      border: 1px solid #ebeef5;
      border-radius: 4px;

      .file-info-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 12px;
        border-bottom: 1px solid #ebeef5;

        &:last-child {
          border-bottom: none;
        }

        .file-name {
          display: flex;
          align-items: center;

          i {
            margin-right: 5px;
            font-size: 16px;
          }
        }

        .file-meta {
          display: flex;
          align-items: center;
          font-size: 12px;
          color: #909399;

          .file-type {
            margin-right: 10px;
          }
        }
      }
    }
  }
</style>
