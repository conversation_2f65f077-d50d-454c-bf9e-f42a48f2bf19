<template>
  <div class="system-log-container">
    <pageHeader :back="false" title="系统日志" />

    <el-card class="box-card" shadow="never">
      <el-form
        :inline="true"
        label-width="100px"
        :model="queryForm"
        @submit.native.prevent
      >
        <el-form-item label="账号" prop="operUserId">
          <el-select
            v-model="queryForm.operUserId"
            clearable
            filterable
            :loading="userInfoLoading"
            placeholder="请输入姓名"
            remote
            :remote-method="remoteMethodUserInfo"
          >
            <el-option
              v-for="(i, k) in userInfoList"
              :key="k"
              :label="i.userInfoName"
              :value="i.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="周期">
          <el-date-picker
            v-model="searchDate"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            start-placeholder="开始日期"
            type="daterange"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
        <el-form-item label="参数关键词">
          <el-input
            v-model.trim="queryForm.operRequParam"
            clearable
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item label="请求方式" prop="operType">
          <el-select
            v-model="queryForm.operType"
            clearable
            filterable
            multiple
            placeholder="请选择"
          >
            <el-option
              v-for="(i, k) in operTypeList"
              :key="k"
              :label="i"
              :value="i"
            />
          </el-select>
        </el-form-item>
        <el-row class="alignCenter">
          <el-button icon="el-icon-search" type="primary" @click="queryData">
            查询
          </el-button>
        </el-row>
      </el-form>
    </el-card>
    <el-pagination
      background
      :current-page="queryForm.pageNum"
      :layout="layout"
      :page-size="queryForm.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <el-table v-loading="listLoading" border :data="list">
      <el-table-column align="center" label="序号" width="60px">
        <template slot-scope="{ $index }">
          {{ queryForm.pageSize * (queryForm.pageNum - 1) + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="操作时间"
        prop="operStartTime"
        show-overflow-tooltip
        width="160px"
      />
      <el-table-column
        align="center"
        label="操作人"
        prop="operUserVo.name"
        show-overflow-tooltip
        width="130px"
      >
        <template slot-scope="{ row }">
          <div v-if="row.operUser">
            <div>
              {{ row.operUser.nickname }}
            </div>
            <div>({{ row.operUser.username }})</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="操作内容"
        prop="operDesc"
        width="150px"
      />
      <el-table-column
        align="center"
        label="操作系统"
        prop="operSystem"
        width="115px"
      />
      <el-table-column
        align="center"
        label="浏览器"
        prop="operBrowser"
        width="95px"
      >
        <template slot-scope="{ row }">
          <span :title="row.operAgent">{{ row.operBrowser }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="IP地址"
        prop="operIp"
        width="130px"
      />
      <el-table-column align="center" label="请求地址" prop="operUri" />
      <el-table-column
        align="center"
        label="请求方式"
        prop="operType"
        width="100px"
      >
        <template slot-scope="{ row }">
          <el-tag v-if="row.operType == 'DELETE'" type="danger">
            {{ row.operType }}
          </el-tag>
          <el-tag v-else-if="row.operType == 'POST'" type="success">
            {{ row.operType }}
          </el-tag>
          <el-tag v-else-if="row.operType == 'PUT'" type="warning">
            {{ row.operType }}
          </el-tag>
          <el-tag v-else type="primary">
            {{ row.operType }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="请求参数"
        prop="operRequParam"
        width="180px"
      />
      <el-table-column align="center" label="请求耗时(ms)" prop="operTime" />

      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="queryForm.pageNum"
      :layout="layout"
      :page-size="queryForm.pageSize"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
  </div>
</template>

<script>
  import { getList } from '@/api/system/systemLog'
  import pageHeader from '@/extra/pageHeader'
  export default {
    name: 'SystemLog',
    components: { pageHeader },
    data() {
      return {
        list: [],
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        searchDate: [],
        queryForm: {
          account: '',
          searchDate: '',
          pageNum: 1,
          pageSize: 30,
          operType: [],
        },
        userInfoList: [],
        userInfoLoading: false,
        operTypeList: ['GET', 'POST', 'PUT', 'DELETE'],
      }
    },
    created() {
      this.fetchData()
    },
    methods: {
      handleSizeChange(val) {
        this.queryForm.pageSize = val
        this.fetchData()
      },
      handleCurrentChange(val) {
        this.queryForm.pageNum = val
        this.fetchData()
      },
      queryData() {
        this.queryForm.pageNum = 1
        this.fetchData()
      },
      async fetchData() {
        this.listLoading = true
        const { operType, ...param } = this.queryForm
        var params = param
        if (operType && operType.length > 0)
          params.operType = operType.toString()
        params.searchDate = this.searchDate.toString()
        const { data, page } = await getList(params)
        this.list = data
        this.total = page.totalCount
        this.listLoading = false
      },
      remoteMethodUserInfo(query) {
        if (query !== '') {
          this.userInfoLoading = true
          setTimeout(async () => {
            // getClientUsers({
            //   // pageSize: 1000,
            //   searchKey: query,
            // }).then(({ list }) => {
            //   this.userInfoList = list.map((i) => {
            //     i.userInfoName = `${i.nickname}(${i.mobile})`
            //     return i
            //   })
            // })
            this.userInfoLoading = false
          }, 200)
        } else {
          this.userInfoList = []
        }
      },
    },
  }
</script>
