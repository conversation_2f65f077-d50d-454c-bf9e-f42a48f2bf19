<template>
  <div class="pageHeader">
    <div style="height: 30px">
      <span
        class="el-page-header__content"
        style="font-weight: bold; line-height: 30px"
      >
        {{ title }}
      </span>
      <el-button v-if="back" size="mini" style="float: right" @click="goBack">
        返回上一页
      </el-button>
      <slot name="extra" />
      <template v-if="others.length > 0">
        <template v-for="(item, index) in others">
          <div :key="index" style="float: right; margin-right: 5px">
            <div
              v-if="item.type == 'iconButton'"
              style="margin-right: 5px; line-height: 30px"
            >
              <vab-icon :icon="item.icon" @click="otherButton(item.click)" />
            </div>
            <el-button
              v-else
              :icon="item.icon"
              size="mini"
              style="margin-right: 5px"
              :type="item.type"
              @click="otherButton(item.click)"
            >
              {{ item.name }}
            </el-button>
          </div>
        </template>
      </template>
    </div>
    <el-divider style="margin-top: 5px" />
  </div>
</template>

<script>
  export default {
    props: {
      title: {
        type: String,
        default: function () {
          return ''
        },
      },
      back: {
        type: Boolean,
        default: function () {
          return true
        },
      },
      others: {
        type: Array,
        default: function () {
          return []
        },
      },
    },
    methods: {
      goBack() {
        this.$router.go(-1)
      },
      otherButton(type) {
        this.$emit(type)
      },
    },
  }
</script>

<style scoped>
  .pageHeader ::v-deep .el-divider {
    margin: 8px 0 15px 0;
  }
</style>
