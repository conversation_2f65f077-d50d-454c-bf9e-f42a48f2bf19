<template>
  <div class="personal-center-container">
    <el-row :gutter="20">
      <el-col :lg="8" :md="12" :sm="24" :xl="8" :xs="24">
        <el-card shadow="hover">
          <div class="personal-center-user-info">
            <div class="personal-center-user-info-full-name">
              {{ form.nickname }}
            </div>
            <ul class="personal-center-user-info-list">
              <li>
                <vab-icon icon="account-circle-line" />
                {{ form.username }}
              </li>
              <li>
                <vab-icon icon="phone-fill" />
                {{ form.mobile }}
              </li>
              <li>
                <vab-icon icon="community-line" />
                {{ form.dept ? form.dept.name : '/' }}
              </li>
              <!-- <li>
                <vab-icon icon="git-repository-line" />
                {{ form.position }}
              </li> -->
              <!-- <li>
                <vab-icon icon="group-fill" />
                <div style="display: inline-grid">
                  <div
                    v-for="(i, k) in form.uniroleActorGroupList"
                    :key="k"
                    style="margin-bottom: 5px"
                  >
                    <el-tag
                      v-for="(ii, index) in i.jobNameList"
                      :key="index"
                      style="margin: 1px"
                      type="primary"
                    >
                      {{ ii }}
                    </el-tag>
                  </div>
                </div>
              </li> -->
            </ul>
          </div>
        </el-card>
      </el-col>
      <el-col :lg="16" :md="12" :sm="24" :xl="16" :xs="24">
        <el-card shadow="hover">
          <el-tabs v-model="activeName">
            <el-tab-pane label="账号基本信息" name="first">
              <el-form
                ref="form"
                label-width="120px"
                :model="form"
                :rules="rules"
              >
                <el-form-item label="用户名：" prop="username">
                  {{ form.username }}
                </el-form-item>
                <el-form-item label="姓名：" prop="nickname">
                  <!-- <el-input v-model="form.nickname" /> -->
                  {{ form.nickname }}
                </el-form-item>
                <el-form-item label="电话：">
                  <!-- <el-input v-model="form.mobile" /> -->
                  {{ form.mobile }}
                </el-form-item>
                <el-form-item label="所在单位：" prop="deptId">
                  <!-- <el-cascader
                    v-model="form.organId"
                    clearable
                    collapse-tags
                    filterable
                    :options="datalist"
                    :props="optionProps"
                    show-all-levels
                  /> -->
                  {{ form.dept ? form.dept.name : '' }}
                </el-form-item>
                <!-- <el-form-item label="职务：">
                  <el-input v-model="form.position" />
                </el-form-item> -->
                <el-form-item>
                  <!-- <el-button type="primary" @click="onSubmit('form')">
                    保存
                  </el-button> -->
                  <el-button plain type="primary" @click="goBack">
                    返回上一页
                  </el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>
            <el-tab-pane label="修改密码" name="editpsw">
              <el-form
                ref="pswform"
                autocomplete="off"
                class="demo-ruleForm"
                label-width="100px"
                :model="pswform"
                :rules="pswrules"
                status-icon
              >
                <el-alert
                  :closable="false"
                  show-icon
                  :title="`密码包括大小写字母，数字，特殊字符(不是字母、数字、下划线、汉字)，长度至少12位`"
                  type="warning"
                />
                <!-- <el-form-item label="原密码：" prop="oldPass">
                  <el-input v-model="pswform.oldPass" type="text" />
                </el-form-item> -->
                <el-form-item label="新密码：" prop="password">
                  <el-input
                    v-model="pswform.password"
                    autocomplete="new-password"
                    type="password"
                  />
                </el-form-item>
                <el-form-item label="确认密码：" prop="checkPass">
                  <el-input
                    v-model="pswform.checkPass"
                    autocomplete="new-password"
                    type="password"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="submitForm('pswform')">
                    提交
                  </el-button>
                  <!-- <el-button @click="resetForm('pswform')">重置</el-button> -->
                </el-form-item>
              </el-form>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import { mapGetters, mapActions } from 'vuex'
  import { editPassWord } from '@/api/user'
  import { getOtherUserInfo, putUser } from '@/api/system/userManagement'
  // import { getUniroleTree } from '@/api/system/departmentManagement'
  // import { handleChildren } from '@/utils/handleNumber'
  import { toLoginRoute } from '@/utils/routes'
  export default {
    name: 'PersonalCenter',
    components: {},
    data() {
      var checkEmail = (rule, value, callback) => {
        // const reg = /^([a-zA-Z0-9_-])+@cnpc.com.cn+/
        if (!value) {
          return callback(new Error('邮箱不能为空'))
        }
        //  else if (!reg.test(value)) {
        //   return callback(
        //     new Error('邮箱不正确，请输入后缀为@cnpc.com.cn的中油邮箱地址')
        //   )
        // }
        else {
          callback()
        }
      }
      var validatePass = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请输入密码'))
        } else {
          if (this.pswform.checkPass !== '') {
            this.$refs.pswform.validateField('checkPass')
          }
          callback()
        }
      }
      var validatePass2 = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请再次输入密码'))
        } else if (value !== this.pswform.password) {
          callback(new Error('两次输入密码不一致!'))
        } else {
          callback()
        }
      }
      return {
        activeName: 'first',
        form: {},
        datalist: [],
        rules: {
          email: [{ required: true, validator: checkEmail, trigger: 'blur' }],
        },
        optionProps: {
          value: 'id',
          label: 'name',
          emitPath: false,
          checkStrictly: true,
        },
        pswform: {
          // oldPass: '',
          checkPass: '',
        },
        pswrules: {
          oldPass: [
            { required: true, trigger: 'blur', message: '请输入原密码' },
          ],
          password: [
            { required: true, validator: validatePass, trigger: 'blur' },
          ],
          checkPass: [
            { required: true, validator: validatePass2, trigger: 'blur' },
          ],
        },
      }
    },
    computed: {
      ...mapGetters({
        username: 'user/username',
        user: 'user/user',
      }),
    },
    created() {
      // this.getOrgan()
      if (this.$route.query.userId) {
        this.getUsersInfo(this.$route.query.userId)
      } else {
        this.getMyInfo()
      }
    },
    methods: {
      ...mapActions({
        _logout: 'user/logout',
      }),
      // async getOrgan() {
      //   const { data, code } = await getUniroleTree()
      //   if (code == 200) {
      //     this.datalist = handleChildren(data)
      //   }
      // },
      async getUsersInfo(id) {
        const { data } = await getOtherUserInfo(id)
        this.form = data
      },
      async getMyInfo() {
        // const { data } = await getMe()
        console.log(this.user)
        this.form = this.user
      },
      onSubmit(formName) {
        this.$refs[formName].validate(async (valid) => {
          if (valid) {
            this.form.accountId = this.$route.query.userId
            const { code, message } = await putUser(this.form)
            if (code == 200) {
              this.$baseMessage('完善人员信息成功！', 'success')
              this.$bus.$emit('user-management-fetch')
              // this.banckForm()
            } else {
              this.$baseMessage(message, 'warning')
            }
          } else {
            return false
          }
        })
      },
      openDialog() {},
      goBack() {
        this.$router.go(-1)
      },

      //修改密码
      submitForm(formName) {
        this.$refs[formName].validate(async (valid) => {
          if (valid) {
            var params = this.pswform
            params.userId = this.form.userId
            const {
              data: { code, msg },
            } = await editPassWord(params)
            if (code == '00000') {
              this.$baseMessage('修改成功，请重新登录！', 'success')
              setTimeout(() => {
                this.logout()
              }, 1000)
            } else {
              this.$baseMessage(msg, 'warning')
            }
          } else {
            return false
          }
        })
      },

      async logout() {
        await this._logout()
        await this.$router.push(toLoginRoute(this.$route.path))
      },
    },
  }
</script>

<style lang="scss" scoped>
  $base: '.personal-center';
  #{$base}-container {
    padding: 0 !important;
    background: $base-color-background !important;

    #{$base}-user-info {
      padding: $base-padding;
      text-align: center;

      ::v-deep {
        .el-avatar {
          img {
            cursor: pointer;
          }
        }
      }

      &-full-name {
        margin-top: 15px;
        font-size: 24px;
        font-weight: 500;
        color: #262626;
      }

      &-description {
        margin-top: 8px;
      }

      &-follow {
        margin-top: 15px;
      }

      &-list {
        margin-top: 18px;
        line-height: 30px;
        text-align: left;
        list-style: none;

        h5 {
          margin: -20px 0 5px 0;
        }

        ::v-deep {
          .el-tag {
            margin-right: 10px !important;
          }

          .el-tag + .el-tag {
            margin-left: 0;
          }
        }
      }
    }

    #{$base}-item {
      display: flex;

      i {
        font-size: 40px;
      }

      &-content {
        box-sizing: border-box;
        flex: 1;
        margin-left: $base-margin;

        &-second {
          margin-top: 8px;
        }
      }
    }
  }

  .el-tag + .el-tag {
    margin-left: 10px;
  }
  .button-new-tag {
    height: 32px;
    padding-top: 0;
    padding-bottom: 0;
    margin-left: 10px;
    line-height: 30px;
  }
  .input-new-tag {
    width: 90px;
    margin-left: 10px;
    vertical-align: bottom;
  }
</style>
