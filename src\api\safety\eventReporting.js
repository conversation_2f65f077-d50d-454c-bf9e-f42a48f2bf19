import request from '@/utils/request'
/**
 *
 * 生产安全事件报告单关联
 */
// 分页获取列表
export function getEventReportingList(params) {
  return request({
    url: '/pc/productionSafetyEvent/list',
    method: 'get',
    params,
  })
}

// 根据ID获取
export function getEventReportingById(id) {
  return request({
    url: `/pc/productionSafetyEvent/${id}`,
    method: 'get',
  })
}

// 暂存
export function saveEventReporting(data) {
  return request({
    url: '/pc/productionSafetyEvent/draft',
    method: 'post',
    data,
  })
}

// 新增
export function addEventReporting(data) {
  return request({
    url: '/pc/productionSafetyEvent/submit',
    method: 'post',
    data,
  })
}

// 修改
export function updateEventReporting(data) {
  return request({
    url: '/pc/productionSafetyEvent/update',
    method: 'put',
    data,
  })
}

// 删除
export function delEventReporting(ids) {
  return request({
    url: '/pc/productionSafetyEvent/delete',
    method: 'delete',
    data: ids,
  })
}

// 导出Word
export function exportEventReporting(id) {
  return request({
    url: '/pc/productionSafetyEvent/exportWord/' + id,
    method: 'get',
  })
}

/**
 *
 * 重要生产安全事件调查单关联
 */
// 根据ID获取
export function getEventReportingRelationById(id) {
  return request({
    url: `/pc/safetyIncidents/${id}`,
    method: 'get',
  })
}

// (暂存)创建数据
export function createEventReportingRelation(data) {
  return request({
    url: '/pc/safetyIncidents/draft',
    method: 'post',
    data,
  })
}

// (提交)创建数据

export function submitEventReportingRelation(data) {
  return request({
    url: '/pc/safetyIncidents/submit',
    method: 'post',
    data,
  })
}

// 分页获取列表
export function getEventReportingRelationList(params) {
  return request({
    url: '/pc/safetyIncidents/list',
    method: 'get',
    params,
  })
}

// 删除对象组
export function delEventReportingRelation(ids) {
  return request({
    url: '/pc/safetyIncidents/delete',
    method: 'delete',
    data: ids,
  })
}

// 修改SafetyIncidents对象
export function updateEventReportingRelation(data) {
  return request({
    url: '/pc/safetyIncidents/update',
    method: 'put',
    data,
  })
}

// 导出Word
export function exportEventReportingRelation(params) {
  return request({
    url: '/pc/safetyIncidents/exportWord',
    method: 'get',
    params,
  })
}
