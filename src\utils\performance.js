/**
 * @description 性能优化工具函数
 */

// 防抖函数
export function debounce(fn, delay = 300) {
  let timer = null

  return function (...args) {
    if (timer) clearTimeout(timer)

    timer = setTimeout(() => {
      fn.apply(this, args)
      timer = null
    }, delay)
  }
}

// 节流函数
export function throttle(fn, interval = 300) {
  let lastTime = 0

  return function (...args) {
    const now = Date.now()

    if (now - lastTime >= interval) {
      fn.apply(this, args)
      lastTime = now
    }
  }
}

// 图片懒加载指令
export const lazyLoadDirective = {
  inserted: (el) => {
    function loadImage() {
      const imageElement = Array.from(el.children).find(
        (el) => el.nodeName === 'IMG'
      )

      if (imageElement) {
        imageElement.addEventListener('load', () => {
          setTimeout(() => el.classList.add('loaded'), 100)
        })
        imageElement.addEventListener('error', () => console.log('error'))
        imageElement.src = imageElement.dataset.src
      }
    }

    function handleIntersect(entries, observer) {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          loadImage()
          observer.unobserve(el)
        }
      })
    }

    function createObserver() {
      const options = {
        root: null,
        threshold: 0,
      }
      const observer = new IntersectionObserver(handleIntersect, options)
      observer.observe(el)
    }

    if (window['IntersectionObserver']) {
      createObserver()
    } else {
      loadImage()
    }
  },
}

// 组件缓存控制
export const keepAliveConfig = {
  // 需要缓存的组件名称
  include: [],

  // 添加缓存组件
  addCache(componentName) {
    if (!this.include.includes(componentName)) {
      this.include.push(componentName)
    }
  },

  // 移除缓存组件
  removeCache(componentName) {
    const index = this.include.indexOf(componentName)
    if (index > -1) {
      this.include.splice(index, 1)
    }
  },

  // 清空所有缓存
  clearCache() {
    this.include = []
  },
}

// 资源预加载
export function preloadResources(resources = []) {
  resources.forEach((resource) => {
    if (resource.type === 'image') {
      const img = new Image()
      img.src = resource.url
    } else if (resource.type === 'script') {
      const script = document.createElement('link')
      script.rel = 'preload'
      script.as = 'script'
      script.href = resource.url
      document.head.appendChild(script)
    } else if (resource.type === 'style') {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.as = 'style'
      link.href = resource.url
      document.head.appendChild(link)
    }
  })
}

// 虚拟滚动助手
export function createVirtualScroll(options = {}) {
  const defaultOptions = {
    itemHeight: 40,
    visibleItems: 10,
    buffer: 5,
  }

  const config = { ...defaultOptions, ...options }

  return {
    // 计算可见项
    getVisibleItems(items = [], scrollTop = 0) {
      const startIndex =
        Math.floor(scrollTop / config.itemHeight) - config.buffer
      const endIndex = startIndex + config.visibleItems + 2 * config.buffer

      return {
        items: items.slice(
          Math.max(0, startIndex),
          Math.min(items.length, endIndex)
        ),
        startIndex: Math.max(0, startIndex),
        endIndex: Math.min(items.length, endIndex),
        paddingTop: Math.max(0, startIndex) * config.itemHeight,
        paddingBottom: Math.max(0, items.length - endIndex) * config.itemHeight,
      }
    },

    // 获取总高度
    getTotalHeight(itemCount) {
      return itemCount * config.itemHeight
    },
  }
}
