import request from '@/utils/request'

export function getToDoList(params) {
  return request({
    url: '/perform-duties-service/examineApprove/list',
    method: 'get',
    params,
  })
}
export function getToDoDetail(id) {
  return request({
    url: '/perform-duties-service/examineApprove/' + id,
    method: 'get',
  })
}

export function getToDoAudit(data) {
  return request({
    url: '/perform-duties-service/examineApprove/auditData',
    method: 'put',
    data,
  })
}

// 用户查询当日待履职清单
export function getToDoListByUser(params) {
  return request({
    url: '/perform-duties-service/perform/userDutyListsInfo/userQueryTodayList',
    method: 'get',
    params,
  })
}
