<template>
  <el-dialog
    :close-on-click-modal="false"
    :title="title"
    :visible.sync="dialogFormVisible"
    width="700px"
    @close="close"
  >
    <el-form ref="form" label-width="100px" :model="form" :rules="rules">
      <el-form-item label="版本号：" prop="versionCode">
        <el-input v-model="form.versionCode" placeholder="请输入版本号" />
      </el-form-item>
      <el-form-item label="更新内容：" prop="content">
        <el-input
          v-model="content"
          placeholder="请输入更新内容,Enter确认"
          @blur="handleAddContent"
          @keyup.enter.native="handleAddContent"
        />
        <div v-if="form.content.length > 0">
          <div v-for="(i, index) in form.content" :key="index">
            {{ index + 1 }}、
            <el-tag closable @close="handleCloseContent(index)">
              {{ i }}
            </el-tag>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="apk文件" prop="name">
        <upload-file
          :accepted="'.apk'"
          :file-list="fileList"
          :limit="1"
          :max-size="500"
          :params="{
            mark: 'downloadAddress',
          }"
          :size-unit="'MB'"
          @uploadsuccess="handleSuccessFile"
        />
      </el-form-item>
      <el-form-item label="详细内容：" prop="detail">
        <nedit
          :params="{ type: 'detail' }"
          :values="form.detail"
          @returnEdit="returnEdit"
        />
      </el-form-item>
      <el-form-item label="立即发布：" prop="onRelease">
        <el-switch
          v-model="form.onRelease"
          active-color="#13ce66"
          active-text="是"
          inactive-color="#ff4949"
          inactive-text="否"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="save">确 定</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import { baseURL } from '@/config'
  import { postApp, editApp } from '@/api/system/app'
  import { getToken } from '@/utils/token'
  import uploadFile from '@/extra/uploadFile/index.vue'
  import nedit from '@/extra/nedit'
  export default {
    name: 'AppEdit',
    components: { uploadFile, nedit },
    data() {
      return {
        form: {
          versionCode: '',
          content: [],
        },
        content: '',
        rules: {
          versionCode: [
            { required: true, trigger: 'blur', message: '请输入版本号' },
          ],
          content: [
            { required: true, trigger: 'blur', message: '请输入更新内容' },
          ],
        },
        title: '',
        dialogFormVisible: false,
        uploadUrl: baseURL + '/uploads/file',
        headers: { token: getToken() },
        fileList: [],
      }
    },
    created() {},
    methods: {
      returnEdit(data) {
        this.form[data.params.type] = data.data
      },
      handleSuccessFile(file, params, type) {
        if (type == 'remove') {
          this.form[params.mark] = null
        } else {
          this.form[params.mark] = file.path
        }
      },
      showEdit(row) {
        // this.fetchData()
        if (!row) {
          this.title = '添加'
        } else {
          this.title = '编辑'
          this.form = Object.assign({}, row)
          this.fileList =
            row.downloadAddress != null
              ? [{ name: '已上传', url: row.downloadAddressUrl }]
              : []
        }
        this.dialogFormVisible = true
      },
      close() {
        this.$refs['form'].resetFields()
        this.form = this.$options.data().form
        this.dialogFormVisible = false
      },
      save() {
        this.$refs['form'].validate(async (valid) => {
          if (valid) {
            var res = {}
            if (this.form.id) {
              res = await editApp(this.form)
            } else {
              res = await postApp(this.form)
            }
            if (res.code == 200) {
              this.$baseMessage('成功！', 'success', 'vab-hey-message-success')
              this.$emit('fetch-data')
              this.close()
            }
          }
        })
      },
      handleAddContent() {
        if (this.content) {
          this.form.content.push(this.content)
          this.content = ''
        }
      },
      handleCloseContent(i) {
        this.form.content.splice(i, 1)
      },
    },
  }
</script>
