import store from '@/store'
import { isArray } from '@/utils/validate'
import Vue from 'vue'

/**
 * 是否可以访问目标权限元素
 * @param target 目标(路由|按钮)要求权限
 * @returns {boolean} 满足访问条件
 */
export function hasPermission(target) {
  if (store.getters['acl/admin']) return true
  if (isArray(target) && target.length > 0)
    return can(
      [...store.getters['acl/role'], ...store.getters['acl/permission']],
      {
        permission: target,
        mode: 'oneOf',
      }
    )
  const { role, permission, mode = 'oneOf' } = target
  let result = true
  if (role)
    result =
      result && can(store.getters['acl/role'], { permission: role, mode })
  if (result && permission)
    result = can(store.getters['acl/permission'], {
      permission,
      mode,
    })
  return result
}

/**
 * 检查是否满足权限
 * @param roleOrPermission 当前用户权限
 * @param target 目标(路由|按钮)要求权限
 * @returns {boolean} 满足访问条件
 */
function can(roleOrPermission, target) {
  let hasRole = false
  const { permission, mode } = target
  if (mode === 'allOf')
    hasRole = permission.every((item) => roleOrPermission.includes(item))
  if (mode === 'oneOf')
    hasRole = permission.some((item) => roleOrPermission.includes(item))
  if (mode === 'except')
    hasRole = !permission.every((item) => roleOrPermission.includes(item))
  return hasRole
}

//按钮权限
export function btn_role(btn) {
  return store.state.acl.btnRole.includes(btn)
}
Vue.prototype.$btn_role = btn_role
//用户权限
export function role(btn) {
  return store.state.acl.role.includes(btn)
}
Vue.prototype.$role = role
//页面内内容块权限
export function content_role(content) {
  return store.state.acl.contentRole.includes(content)
}
Vue.prototype.$content_role = content_role
