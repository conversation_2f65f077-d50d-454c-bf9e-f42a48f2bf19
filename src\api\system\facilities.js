import request from '@/utils/request'

// 创建数据
export function addEquipmentSites(data) {
  return request({
    url: '/perform-duties-service/system/equipmentSites',
    method: 'post',
    data,
  })
}

// 分页获取列表
export function getEquipmentSitesList(params) {
  return request({
    url: '/perform-duties-service/system/equipmentSites/list',
    method: 'get',
    params,
  })
}

//删除对象组
export function delEquipmentSites(params) {
  return request({
    url: '/perform-duties-service/system/equipmentSites/delete',
    method: 'delete',
    params,
  })
}
