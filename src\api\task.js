import request from '@/utils/request'
/**
 * 任务提交审核
 * params：id任务i,state审核意见：1通过，2退回,transactorContent审核说明
 */
export function submitTask(data) {
  return request({
    url: `/workflowTask/review`,
    method: 'post',
    data,
  })
}

/**
 * 待办任务
 */
export function getTodoTask(params) {
  return request({
    url: `/workflowTask/doing`,
    method: 'get',
    params,
  })
}
/**
 * 所有待办任务
 */
export function getAllTodoTask(params) {
  return request({
    url: `/workflowTask/allDoing`,
    method: 'get',
    params,
  })
}

/**
 * 已办任务
 */
export function getOverTask(params) {
  return request({
    url: `/workflowTask/overTask`,
    method: 'get',
    params,
  })
}

/**
 * 撤销申请
 * taskId为流程id
 */
export function revokeTask(taskId) {
  return request({
    url: `/workflowTask/undo?taskId=${taskId}`,
    method: 'post',
  })
}
/**
 * 作废申请
 */
export function removeTask(data) {
  return request({
    url: `/`,
    method: 'post',
    data,
  })
}

/**
 * 流程重置
 * @param {*} data
 * @returns
 */
export function reStartWorkflow(data, tableName) {
  return request({
    url: `/planApp/${tableName}/reStartWorkflow?ids=${data.id}`,
    method: 'post',
    data,
  })
}
