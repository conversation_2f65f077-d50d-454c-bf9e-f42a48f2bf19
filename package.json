{"author": "wdysoft", "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@logicflow/core": "^1.0.2", "@logicflow/extension": "^1.0.2", "axios": "^1.4.0", "babel-plugin-dynamic-import-node": "^2.3.3", "clipboard": "^2.0.11", "core-js": "^3.30.2", "cross-env": "^7.0.3", "dayjs": "^1.11.8", "echarts": "^5.4.2", "element-ui": "^2.15.13", "file-saver": "^2.0.5", "js-cookie": "^3.0.5", "jsencrypt": "^3.3.2", "jsplumb": "^2.15.6", "lodash": "^4.17.21", "mockjs": "^1.1.0", "moment": "^2.29.4", "nprogress": "^0.2.0", "picocolors": "^1.1.1", "qrcodejs2": "0.0.2", "qs": "^6.11.2", "register-service-worker": "^1.7.2", "resize-detector": "^0.3.0", "screenfull": "5.2.0", "vab-icons": "file:vab-icons", "vue": "^2.7.14", "vue-i18n": "^8.28.2", "vue-json-viewer": "^2.2.22", "vue-router": "^3.6.5", "vue-ueditor-wrap": "^2.5.6", "vuedraggable": "^2.24.3", "vuex": "^3.6.2", "xlsx": "^0.17.4"}, "devDependencies": {"@vitejs/plugin-vue2": "^2.2.0", "@vitejs/plugin-legacy": "^4.1.1", "vite": "^4.4.9", "vite-plugin-html": "^3.2.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-pwa": "^0.16.5", "@vue/eslint-config-prettier": "6.0.0", "body-parser": "^1.20.2", "call-rely": "^1.3.1", "chalk": "^4.1.2", "chokidar": "^3.5.3", "dotenv": "^16.3.1", "eslint": "6.8.0", "eslint-plugin-prettier": "3.4.1", "eslint-plugin-vue": "^9.17.0", "lint-staged": "^13.2.2", "postcss": "^8.4.24", "postcss-html": "^1.5.0", "postcss-jsx": "^0.36.4", "postcss-scss": "^4.0.6", "postcss-syntax": "^0.36.2", "prettier": "^2.8.8", "sass": "^1.63.6", "stylelint": "^15.7.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recess-order": "^4.2.0", "vue-eslint-parser": "^9.3.2"}, "gitHooks": {"pre-commit": "lint-staged"}, "homepage": "https://chu1204505056.gitee.io/admin-pro", "license": "Mozilla Public License Version 2.0", "lint-staged": {"*.{js,jsx,vue}": ["vue-cli-service lint", "git add"]}, "name": "admin-pro", "participants": ["fwfmiao"], "private": true, "repository": {"type": "git", "url": "git+https://github.com/vue-admin-beautiful/admin-pro.git"}, "scripts": {"build-dev": "cross-env VUE_APP_MODE=dev vue-cli-service build", "build-prod": "cross-env VUE_APP_MODE=prod vue-cli-service build", "build-gmbicloud-test": "cross-env VUE_APP_MODE=gmbicloud_test vue-cli-service build", "build-gmbicloud-local": "cross-env VUE_APP_MODE=gmbicloud_test vue-cli-service build", "global:install": "npm install -g nrm cnpm npm-check-updates", "globle:update": "ncu -g", "lint": "vue-cli-service lint", "lint:eslint": "eslint {src,mock}/**/*.{vue,js} --fix", "lint:prettier": "prettier {src,mock}/**/*.{html,vue,css,sass,scss,js,md} --write", "lint:stylelint": "stylelint {src,mock}/**/*.{html,vue,css,sass,scss} --fix --cache --cache-location node_modules/.cache/stylelint/", "local": "cross-env VUE_APP_MODE=local vue-cli-service serve", "module:install": "npm i", "module:reinstall": "rimraf node_modules && npm run module:install", "module:update": "ncu -u --reject chalk,@logicflow/core,@logicflow/extension,screenfull,@vue/eslint-config-prettier,compression-webpack-plugin,eslint,eslint-plugin-prettier,filemanager-webpack-plugin,sass,sass-loader,webpack && npm run module:install", "nrm:npm": "nrm use npm", "nrm:taobao": "nrm use taobao", "serve": "cross-env VUE_APP_MODE=dev vue-cli-service serve", "serve-dev": "cross-env VUE_APP_MODE=dev vue-cli-service serve", "serve-prod": "cross-env VUE_APP_MODE=prod vue-cli-service serve", "analyze": "cross-env VUE_APP_MODE=dev vue-cli-service build --report"}, "version": "2.7.0"}