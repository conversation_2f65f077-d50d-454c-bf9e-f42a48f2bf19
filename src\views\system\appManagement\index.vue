<template>
  <div class="department-management-container">
    <pageHeader
      :back="false"
      :others="otherBtn"
      title="APP端版本管理"
      @addnew="handleEdit"
    />
    <vab-query-form>
      <el-form label-width="100px" :model="queryForm" @submit.native.prevent>
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="版本号">
              <el-input
                v-model.trim="queryForm.versionCode"
                clearable
                placeholder="请输入版本号"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="内容">
              <el-input
                v-model.trim="queryForm.content"
                clearable
                placeholder="请输入内容关键词"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否发布">
              <el-select
                v-model="queryForm.onRelease"
                clearable
                placeholder="请选择"
                style="width: 100%"
              >
                <el-option label="已发布" :value="true" />
                <el-option label="未发布" :value="false" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="发布时间">
              <el-date-picker
                v-model="queryForm.updateTimess"
                clearable
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                range-separator="~"
                start-placeholder="开始日期"
                style="width: 100%"
                type="daterange"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item class="alignCenter">
          <el-button icon="el-icon-search" type="primary" @click="queryData">
            查询
          </el-button>
          <!--  -->
        </el-form-item>
      </el-form>
    </vab-query-form>

    <el-table v-loading="listLoading" border :data="list">
      <el-table-column align="center" label="序号" type="index" width="60">
        <template slot-scope="{ $index }">
          {{ pageSize * (pageNum - 1) + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="版本号"
        prop="versionCode"
        show-overflow-tooltip
        width="120"
      />
      <el-table-column align="left" label="更新内容" prop="content">
        <template slot-scope="{ row }">
          <div v-for="(i, index) in row.content" :key="index">
            {{ index + 1 }}、{{ i }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="下载地址"
        prop="downloadAddress"
        show-overflow-tooltip
        width="120"
      >
        <template slot-scope="{ row }">
          <el-button
            v-if="row.downloadAddressUrl"
            @click="downloadApp(row.downloadAddressUrl)"
          >
            下载
          </el-button>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="发布时间"
        prop="updatedAt"
        show-overflow-tooltip
        width="170"
      />
      <el-table-column
        align="center"
        label="状态"
        prop="enable"
        show-overflow-tooltip
        width="120"
      >
        <template slot-scope="scope">
          <el-tag
            size="mini"
            :type="scope.row.onRelease == false ? 'danger' : 'primary'"
          >
            {{ scope.row.onRelease == false ? '未发布' : '已发布' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="250">
        <template #default="{ row }">
          <!-- v-if="!row.onRelease" -->
          <el-button size="mini" type="primary" @click="handleEdit(row)">
            编辑
          </el-button>
          <el-button
            v-if="!row.onRelease"
            plain
            size="mini"
            type="danger"
            @click="handleRedo(row)"
          >
            立即发布
          </el-button>
          <el-button
            v-if="row.onRelease"
            size="mini"
            type="danger"
            @click="handleRedo(row)"
          >
            撤销发布
          </el-button>
          <el-button
            v-if="!row.onRelease"
            size="mini"
            type="danger"
            @click="handleDel(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      :current-page="pageNum"
      :layout="layout"
      :page-size="pageSize"
      :page-sizes="[10, 20, 50, 100, 200, 500]"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <edit ref="edit" @fetch-data="fetchData" />
  </div>
</template>

<script>
  import { delApp, getAppList, redoApp } from '@/api/system/app'
  import pageHeader from '@/extra/pageHeader'
  import Edit from './components/appEdit'

  export default {
    name: 'DepartmentManagement',
    components: { pageHeader, Edit },
    data() {
      return {
        otherBtn: [],
        list: [],
        listLoading: true,

        selectRows: '',
        queryForm: {},
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        pageNum: 1,
        pageSize: 10,
      }
    },
    created() {
      this.otherBtn.push({
        name: '添加',
        icon: 'el-icon-plus',
        type: 'success',
        click: 'addnew',
      })
      // this.fetchData()
    },
    methods: {
      setSelectRows(val) {
        this.selectRows = val
      },
      handleEdit(row) {
        if (row) {
          this.$refs['edit'].showEdit(row)
        } else {
          this.$refs['edit'].showEdit()
        }
      },
      handleDel(row) {
        let ids = ''
        if (row) {
          ids = [row.id].toString()
        }
        this.$confirm('是否删除？').then(async () => {
          const { code } = await delApp({ ids: ids })
          if (code == 200) {
            this.$baseMessage('删除成功！', 'success')
            this.fetchData()
          }
        })
      },
      handleRedo(row) {
        this.$confirm(`是否${row.onRelease ? '撤销发布' : '立即发布'}？`).then(
          async () => {
            const { code } = await redoApp(row.id)
            if (code == 200) {
              this.$baseMessage(
                `${row.onRelease ? '撤销发布' : '立即发布'}成功！`,
                'success'
              )
              this.fetchData()
            }
          }
        )
      },
      handleSizeChange(val) {
        this.pageSize = val
        this.fetchData()
      },
      handleCurrentChange(val) {
        this.pageNum = val
        this.fetchData()
      },
      queryData() {
        this.fetchData()
      },
      async fetchData() {
        this.listLoading = true
        const { updateTimess, ...params } = this.queryForm
        if (updateTimess == null) {
          params.updatedTimes = ''
        } else {
          params.updatedTimes = updateTimess[0] + ',' + updateTimess[1]
        }
        params.pageNum = this.pageNum
        params.pageSize = this.pageSize
        const { code, data, page } = await getAppList(params)
        if (code == 200) {
          this.list = data
          this.total = page ? page.totalCount : 0
          this.listLoading = false
        }
      },
      downloadApp(url) {
        if (url) {
          window.open(url)
        }
      },
    },
  }
</script>
