<template>
  <el-dialog
    :before-close="handleClose"
    title="位置选择"
    :visible.sync="visible"
    width="800px"
  >
    <div class="map-container">
      <div id="container" class="map"></div>
      <div class="search-box">
        <el-input
          v-model="searchKeyword"
          placeholder="请输入地址"
          @keyup.enter.native="searchPlace"
        >
          <el-button slot="append" icon="el-icon-search" @click="searchPlace" />
        </el-input>
      </div>
    </div>
    <div v-if="selectedLocation" class="location-info">
      <p>当前选择: {{ selectedLocation.address }}</p>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="confirmLocation">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import AMapLoader from '@amap/amap-jsapi-loader'
  window._AMapSecurityConfig = {
    securityJsCode: '6660a30f74d095ebfc1da4d31d149333',
  }
  export default {
    name: 'MapPicker',
    data() {
      return {
        visible: false,
        map: null,
        marker: null,
        searchKeyword: '',
        selectedLocation: null,
        AMapObj: null,
        geocoder: null,
      }
    },
    methods: {
      show() {
        this.visible = true
        this.$nextTick(() => {
          this.initMap()
        })
      },
      handleClose() {
        this.visible = false
        this.selectedLocation = null
      },
      async initMap() {
        try {
          const AMap = await AMapLoader.load({
            key: '45cfc3dc128f115ea3803aac750cfda8',
            version: '2.0',
            plugins: [
              'AMap.ToolBar',
              'AMap.Scale',
              'AMap.Geolocation',
              'AMap.Geocoder',
              'AMap.PlaceSearch',
            ],
          })

          this.AMapObj = AMap
          this.map = new AMap.Map('container', {
            zoom: 13,
            resizeEnable: true,
          })

          // 添加工具条和比例尺
          this.map.addControl(new AMap.ToolBar())
          this.map.addControl(new AMap.Scale())
          this.map.addControl(
            new AMap.Geolocation({
              position: 'RB',
              buttonOffset: new AMap.Pixel(10, 20),
              zoomToAccuracy: true,
            })
          )

          // 初始化地理编码服务
          this.geocoder = new AMap.Geocoder({
            city: '全国',
          })

          // 点击地图事件
          this.map.on('click', (e) => {
            this.setMarker(e.lnglat)
            this.getAddress(e.lnglat)
          })
        } catch (error) {
          console.error('地图初始化失败:', error)
          this.$message.error('地图加载失败')
        }
      },
      setMarker(lnglat) {
        if (this.marker) {
          this.marker.setMap(null)
        }
        this.marker = new this.AMapObj.Marker({
          position: lnglat,
          map: this.map,
        })
      },
      async getAddress(lnglat) {
        try {
          const result = await new Promise((resolve, reject) => {
            this.geocoder.getAddress(lnglat, (status, result) => {
              status === 'complete'
                ? resolve(result)
                : reject(new Error('获取地址失败'))
            })
          })
          this.selectedLocation = {
            address: result.regeocode.formattedAddress,
            longitude: lnglat.getLng(),
            latitude: lnglat.getLat(),
          }
        } catch (error) {
          this.$message.error('获取地址信息失败')
        }
      },
      searchPlace() {
        if (!this.searchKeyword) return
        const placeSearch = new this.AMapObj.PlaceSearch({
          map: this.map,
        })
        placeSearch.search(this.searchKeyword)
      },
      confirmLocation() {
        if (!this.selectedLocation) {
          this.$message.warning('请先选择位置')
          return
        }
        this.$emit('confirm', this.selectedLocation)
        this.handleClose()
      },
    },
  }
</script>

<style scoped>
  .map-container {
    position: relative;
    width: 100%;
    height: 400px;
  }
  .map {
    width: 100%;
    height: 100%;
  }
  .search-box {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 1;
    width: 300px;
  }
  .location-info {
    padding: 10px;
    margin-top: 10px;
    background: #f5f7fa;
  }
</style>
