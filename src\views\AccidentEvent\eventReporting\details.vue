<template>
  <div>
    <!-- 重要生产安全事件调查单组件 -->
    <safety-investigation-form
      :event-data="form"
      :visible.sync="investigationFormVisible"
      @close="onInvestigationFormClose"
    />

    <el-drawer
      ref="drawer"
      :before-close="close"
      :close-on-click-modal="false"
      custom-class="event-report-drawer"
      direction="rtl"
      size="100%"
      :title="title + '生产安全事件报告单'"
      :visible.sync="dialogFormVisible"
      @close="close"
    >
      <div class="drawer-container">
        <div class="drawer-content">
          <el-form
            ref="form"
            label-width="150px"
            :model="form"
            :rules="rules"
            style="height: 100%"
          >
            <div class="form-card">
              <div class="section-header">基本信息</div>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="报告人：" prop="reportPersonId">
                    <el-select
                      v-model="form.reportPersonId"
                      clearable
                      filterable
                      placeholder="请选择"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in storeUserList"
                        :key="item.userId"
                        :label="item.nickname"
                        :value="item.userId"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="报告时间：" prop="reportTime">
                    <el-date-picker
                      v-model="form.reportTime"
                      placeholder="选择日期时间"
                      style="width: 100%"
                      type="datetime"
                      value-format="yyyy-MM-dd HH:mm:ss"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="发生单位：" prop="organOrProject">
                    <el-input
                      v-model="form.organOrProject"
                      placeholder="请输入单位或项目名称"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="项目名称：" prop="organOrProject">
                    <el-input
                      v-model="form.organOrProject"
                      placeholder="请输入单位或项目名称"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="目击者：" prop="witness">
                    <el-select
                      v-model="form.witness"
                      clearable
                      filterable
                      placeholder="请选择"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in storeUserList"
                        :key="item.userId"
                        :label="item.nickname"
                        :value="item.userId"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="电话：" prop="witnessPhone">
                    <el-input
                      v-model="form.witnessPhone"
                      placeholder="请输入联系电话"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="发生时间：" prop="happenTime">
                    <el-date-picker
                      v-model="form.happenTime"
                      placeholder="选择日期时间"
                      style="width: 100%"
                      type="datetime"
                      value-format="yyyy-MM-dd HH:mm:ss"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="发生地点：" prop="happenPlace">
                    <el-input
                      v-model="form.happenPlace"
                      placeholder="请输入发生地点"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="事件性质：" prop="property">
                    <el-select
                      v-model="form.property"
                      placeholder="请选择事件性质"
                      style="width: 100%"
                    >
                      <el-option label="限工" :value="1" />
                      <el-option label="医疗" :value="2" />
                      <el-option label="急救（箱）" :value="3" />
                      <el-option label="经济损失" :value="4" />
                      <el-option label="未遂" :value="5" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="事件类别：" prop="eventCategory">
                    <el-select
                      v-model="form.eventCategory"
                      clearable
                      filterable
                      placeholder="请选择"
                      style="width: 100%"
                    >
                      <el-option label="工业生产安全事件" :value="1" />
                      <el-option label="道路交通事件" :value="2" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row v-if="form.eventCategory === 1" :gutter="20">
                <el-col :span="12">
                  <el-form-item label="类别细分：" prop="categorySegmentation">
                    <el-select
                      v-model="form.categorySegmentation"
                      clearable
                      filterable
                      placeholder="请选择"
                      style="width: 100%"
                    >
                      <!-- 灼烫、泄露、塌陷、高处坠落、滑倒滑落、割刺擦伤、触电、起重伤害、机械伤害、物体打击、健康伤害、其他伤害 -->
                      <el-option label="灼烫" :value="1" />
                      <el-option label="泄露" :value="2" />
                      <el-option label="塌陷" :value="3" />
                      <el-option label="高处坠落" :value="4" />
                      <el-option label="滑倒滑落" :value="5" />
                      <el-option label="割刺擦伤" :value="6" />
                      <el-option label="触电" :value="7" />
                      <el-option label="起重伤害" :value="8" />
                      <el-option label="机械伤害" :value="9" />
                      <el-option label="物体打击" :value="10" />
                      <el-option label="健康伤害" :value="11" />
                      <el-option label="其他伤害" :value="12" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="事件经过描述：" prop="courseOfEvent">
                    <el-input
                      v-model="form.courseOfEvent"
                      placeholder="请详细描述事件经过"
                      :rows="4"
                      style="width: 100%"
                      type="textarea"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <div class="form-card">
              <div class="section-header">
                受伤人员基本信息（有人员受伤时填写）
                <div class="section-actions">
                  <el-button
                    icon="el-icon-plus"
                    size="small"
                    type="primary"
                    @click="addInjuredPerson"
                  >
                    新增
                  </el-button>
                </div>
              </div>

              <div
                v-for="(person, index) in form.safetyEventInjuredDtoList"
                :key="index"
                class="injured-person-section"
              >
                <div class="injured-person-header">
                  <span>受伤人员 #{{ index + 1 }}</span>
                  <el-button
                    v-if="form.safetyEventInjuredDtoList.length > 1"
                    icon="el-icon-delete"
                    size="mini"
                    type="danger"
                    @click="removeInjuredPerson(index)"
                  >
                    删除
                  </el-button>
                </div>

                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item
                      :label="'姓名：'"
                      :prop="
                        'safetyEventInjuredDtoList.' + index + '.injuredPerson'
                      "
                    >
                      <el-input
                        v-model="person.injuredPerson"
                        placeholder="请输入受伤人员姓名"
                        style="width: 100%"
                      />
                      <!-- <el-select
                        v-model="person.injuredPerson"
                        clearable
                        filterable
                        placeholder="请选择受伤人员"
                        style="width: 100%"
                      >
                        <el-option
                          v-for="item in storeUserList"
                          :key="item.userId"
                          :label="item.nickname"
                          :value="item.userId"
                        />
                      </el-select> -->
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      :label="'性别：'"
                      :prop="'safetyEventInjuredDtoList.' + index + '.sex'"
                    >
                      <el-radio-group v-model="person.sex" style="width: 100%">
                        <el-radio :label="1">男</el-radio>
                        <el-radio :label="2">女</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item
                      :label="'电话：'"
                      :prop="'safetyEventInjuredDtoList.' + index + '.phone'"
                    >
                      <el-input
                        v-model="person.phone"
                        placeholder="请输入联系电话"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      :label="'出生日期：'"
                      :prop="'safetyEventInjuredDtoList.' + index + '.birthday'"
                    >
                      <el-date-picker
                        v-model="person.birthday"
                        placeholder="选择日期"
                        style="width: 100%"
                        type="date"
                        value-format="yyyy-MM-dd"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item
                      :label="'工种：'"
                      :prop="'safetyEventInjuredDtoList.' + index + '.typeWork'"
                    >
                      <el-input
                        v-model="person.typeWork"
                        placeholder="请输入工种"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      :label="'从事目前岗位年限：'"
                      :prop="
                        'safetyEventInjuredDtoList.' +
                        index +
                        '.yearsExperience'
                      "
                    >
                      <el-input-number
                        v-model="person.yearsExperience"
                        :min="0"
                        placeholder="请输入年限"
                        :precision="1"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item
                      :label="'聘用日期：'"
                      :prop="
                        'safetyEventInjuredDtoList.' + index + '.dateEmployment'
                      "
                    >
                      <el-date-picker
                        v-model="person.dateEmployment"
                        placeholder="选择日期"
                        style="width: 100%"
                        type="date"
                        value-format="yyyy-MM-dd"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      :label="'用工性质：'"
                      :prop="
                        'safetyEventInjuredDtoList.' +
                        index +
                        '.natureEmployment'
                      "
                    >
                      <el-select
                        v-model="person.natureEmployment"
                        placeholder="请选择用工性质"
                        style="width: 100%"
                      >
                        <el-option label="合同工" :value="1" />
                        <el-option label="季节工" :value="2" />
                        <el-option label="承包商" :value="3" />
                        <el-option label="社会人员" :value="4" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="24">
                    <el-form-item
                      :label="'承包商名称：'"
                      :prop="
                        'safetyEventInjuredDtoList.' + index + '.contractor'
                      "
                    >
                      <el-input
                        v-model="person.contractor"
                        placeholder="请输入承包商名称"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item
                      :label="'受伤部位：'"
                      :prop="
                        'safetyEventInjuredDtoList.' + index + '.injuredPart'
                      "
                    >
                      <el-input
                        v-model="person.injuredPart"
                        placeholder="请描述受伤部位"
                        :rows="4"
                        style="width: 100%"
                        type="textarea"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      :label="'治疗情况描述：'"
                      :prop="
                        'safetyEventInjuredDtoList.' +
                        index +
                        '.birefDescription'
                      "
                    >
                      <el-input
                        v-model="person.birefDescription"
                        placeholder="请描述治疗情况"
                        :rows="4"
                        style="width: 100%"
                        type="textarea"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item
                      label="直接经济损失："
                      prop="directEconomicLoss"
                    >
                      <el-input
                        v-model="form.directEconomicLoss"
                        placeholder="请输入金额"
                        style="width: 100%"
                      >
                        <template slot="append">元</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      label="间接经济损失："
                      prop="indirectEconomicLoss"
                    >
                      <el-input
                        v-model="form.indirectEconomicLoss"
                        placeholder="请输入金额"
                        style="width: 100%"
                      >
                        <template slot="append">元</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-divider
                  v-if="index < form.safetyEventInjuredDtoList.length - 1"
                />
              </div>
            </div>

            <div class="form-card">
              <div class="section-header">原因分析</div>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="直接原因：" prop="immediateCause">
                    <el-input
                      v-model="form.immediateCause"
                      placeholder="请输入直接原因"
                      :rows="4"
                      style="width: 100%"
                      type="textarea"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="间接原因：" prop="remoteCause">
                    <el-input
                      v-model="form.remoteCause"
                      placeholder="请输入间接原因"
                      :rows="4"
                      style="width: 100%"
                      type="textarea"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="管理原因：" prop="manageCause">
                    <el-input
                      v-model="form.manageCause"
                      placeholder="请输入管理原因"
                      :rows="4"
                      style="width: 100%"
                      type="textarea"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item
                    label="分析人员单位、姓名："
                    prop="analystOrgan"
                  >
                    <!-- <el-input
                      v-model="form.analystOrgan"
                      placeholder="请输入分析人员单位、姓名"
                      style="width: 100%"
                    /> -->
                    <el-select
                      v-model="form.analystOrgan"
                      clearable
                      filterable
                      placeholder="请选择"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in storeUserList"
                        :key="item.userId"
                        :label="item.nickname"
                        :value="item.userId"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <div class="form-card">
              <div class="section-header">防范措施与审核</div>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="防范措施：" prop="precautionaryMeasure">
                    <el-input
                      v-model="form.precautionaryMeasure"
                      placeholder="请输入防范措施"
                      :rows="4"
                      style="width: 100%"
                      type="textarea"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="措施责任人：" prop="measurePerson">
                    <el-input
                      v-model="form.measurePerson"
                      placeholder="请输入措施责任人"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="审核意见：" prop="auditOpinion">
                    <el-input
                      v-model="form.auditOpinion"
                      placeholder="请输入审核意见"
                      :rows="4"
                      style="width: 100%"
                      type="textarea"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="日期：" prop="auditTime">
                    <el-date-picker
                      v-model="form.auditTime"
                      placeholder="选择日期"
                      style="width: 100%"
                      type="date"
                      value-format="yyyy-MM-dd"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="事件单位责任人：" prop="auditPerson">
                    <el-select
                      v-model="form.auditPerson"
                      clearable
                      filterable
                      placeholder="请选择"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in storeUserList"
                        :key="item.userId"
                        :label="item.nickname"
                        :value="item.userId"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-form>
        </div>
        <div class="drawer-footer">
          <el-button size="medium" @click="close">取 消</el-button>
          <el-button size="medium" type="primary" @click="submitForm">
            暂 存
          </el-button>
          <el-button size="medium" type="primary" @click="save">
            提 交
          </el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script>
  import { mapState } from 'vuex'
  import {
    getEventReportingById,
    saveEventReporting,
    addEventReporting,
  } from '@/api/safety/eventReporting'
  import SafetyInvestigationForm from './safetyInvestigationForm.vue'

  export default {
    name: '',
    components: {
      SafetyInvestigationForm,
    },
    data() {
      return {
        form: {
          safetyEventInjuredDtoList: [
            {
              injuredPerson: '',
              sex: 1,
              phone: '',
              birthday: '',
              typeWork: '',
              yearsExperience: 0,
              dateEmployment: '',
              natureEmployment: '',
              contractor: '',
              injuredPart: '',
              birefDescription: '',
            },
          ],
        },
        content: '',
        rules: {
          templateName: [
            { required: true, trigger: 'blur', message: '内容不能为空' },
          ],
        },
        title: '',
        dialogFormVisible: false,
        investigationFormVisible: false, // 控制调查单弹窗显示
      }
    },
    computed: {
      ...mapState({
        userInfo: (state) => state.user.user,
        storeUserList: (state) => state.user.userList,
      }),
    },
    methods: {
      showEdit(row) {
        if (!row) {
          this.title = '添加'
        } else {
          this.title = '编辑'
          this.form = row
        }
        this.dialogFormVisible = true
      },
      getDetails(id) {
        getEventReportingById(id).then((res) => {
          this.form = res.data
        })
      },
      close() {
        this.$refs['form'].resetFields()
        this.form = this.$options.data().form
        this.dialogFormVisible = false
      },
      // 添加受伤人员
      addInjuredPerson() {
        this.form.safetyEventInjuredDtoList.push({
          injuredPerson: '',
          sex: 1,
          phone: '',
          birthday: '',
          typeWork: '',
          yearsExperience: 0,
          dateEmployment: '',
          natureEmployment: '',
          contractor: '',
          injuredPart: '',
          birefDescription: '',
        })

        // 动态添加表单验证规则
        const index = this.form.safetyEventInjuredDtoList.length - 1
        this.$set(this.rules, `safetyEventInjuredDtoList.${index}.name`, [
          { required: true, trigger: 'blur', message: '姓名不能为空' },
        ])
        this.$set(this.rules, `safetyEventInjuredDtoList.${index}.phone`, [
          { required: true, trigger: 'blur', message: '电话不能为空' },
        ])
      },

      // 删除受伤人员
      removeInjuredPerson(index) {
        if (this.form.safetyEventInjuredDtoList.length <= 1) {
          this.$message.warning('至少保留一条受伤人员信息')
          return
        }
        this.form.safetyEventInjuredDtoList.splice(index, 1)
      },

      // 处理调查单关闭事件
      onInvestigationFormClose() {
        // this.close()
        this.investigationFormVisible = false
      },

      // 检查是否需要显示调查单
      checkShowInvestigationForm() {
        return this.form.property === 1 || this.form.property === 2
      },

      submitForm() {
        // this.investigationFormVisible = true
        saveEventReporting(this.form).then((res) => {
          if (res.code === 200) {
            this.$message.success('暂存成功')

            // 检查是否需要显示调查单
            if (this.checkShowInvestigationForm()) {
              this.investigationFormVisible = true
            } else {
              this.close()
            }
          }
        })
      },

      save() {
        this.$refs['form'].validate((valid) => {
          if (valid) {
            // 检查是否至少有一个受伤人员信息
            if (this.form.safetyEventInjuredDtoList.length === 0) {
              this.$message.warning('请至少添加一条受伤人员信息')
              return false
            }

            // 检查必填字段
            let hasEmptyRequired = false
            this.form.safetyEventInjuredDtoList.forEach((person, index) => {
              if (!person.name || !person.phone) {
                hasEmptyRequired = true
                this.$message.warning(
                  `第${index + 1}条受伤人员信息的姓名和电话不能为空`
                )
              }
            })

            if (hasEmptyRequired) {
              return false
            }

            // 提交表单
            addEventReporting(this.form).then(() => {
              this.$message.success('提交成功')

              // 检查是否需要显示调查单
              if (this.checkShowInvestigationForm()) {
                this.investigationFormVisible = true
              } else {
                this.close()
              }
            })
          } else {
            return false
          }
        })
      },
    },
  }
</script>
<style lang="scss" scoped>
  .drawer-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #f5f7fa;
  }

  .drawer-content {
    height: calc(100% - 69px);
    padding: 20px;
    padding-top: 10px;
    overflow-y: auto;

    .section-header {
      position: relative;
      padding-left: 12px;
      margin: 24px 0 16px;
      font-size: 16px;
      font-weight: 600;
      color: #111;
      border-left: 4px solid #409eff;

      &:before {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        width: 4px;
        content: '';
        background-color: #409eff;
        border-radius: 2px;
      }
    }

    .form-card {
      padding: 20px;
      margin-bottom: 20px;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    }
  }

  .detail-section {
    margin-top: 24px;
  }

  .drawer-footer {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 10;
    padding: 16px;
    text-align: center;
    background: #fff;
    border-top: 1px solid #e8e8e8;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);

    .el-button {
      padding: 10px 24px;
      margin-left: 12px;
      font-weight: 500;
      border-radius: 4px;

      &:first-child {
        margin-left: 0;
      }
    }
  }

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .injured-person-section {
    position: relative;
    padding: 10px 0;

    .injured-person-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-bottom: 10px;
      margin-bottom: 15px;
      border-bottom: 1px dashed #e0e0e0;

      span {
        font-size: 16px;
        font-weight: 500;
        color: #409eff;
      }
    }
  }

  // 调整表单样式
  ::v-deep {
    // 抽屉样式优化
    .event-report-drawer {
      .el-drawer__header {
        padding: 16px 20px;
        margin-bottom: 0;
        font-size: 18px;
        font-weight: 600;
        text-align: center;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

        .el-drawer__close-btn {
          font-size: 20px;
          color: #555;

          &:hover {
            color: #000;
          }
        }
      }

      .el-drawer__body {
        padding: 0;
      }
    }

    // 表单项样式
    .el-form-item {
      margin-bottom: 24px;

      &__label {
        font-weight: 500;
        color: #606266;
      }

      &__error {
        padding-top: 4px;
      }
    }

    // 输入框样式
    .el-input,
    .el-textarea {
      .el-input__inner,
      .el-textarea__inner {
        border-color: #dcdfe6;
        border-radius: 4px;
        transition: all 0.3s;

        &:hover {
          border-color: #c0c4cc;
        }

        &:focus {
          border-color: #409eff;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }
      }
    }

    // 日期选择器样式
    .el-date-editor {
      width: 100% !important;

      .el-input__prefix {
        color: #909399;
      }
    }

    // 下拉选择器样式
    .el-select {
      width: 100%;

      .el-input__inner {
        &:hover {
          border-color: #c0c4cc;
        }

        &:focus {
          border-color: #409eff;
        }
      }
    }

    // 单选按钮样式
    .el-radio {
      margin-right: 20px;

      &__inner {
        &::after {
          width: 6px;
          height: 6px;
        }
      }

      &__input.is-checked + .el-radio__label {
        color: #409eff;
      }
    }

    // 数字输入框样式
    .el-input-number {
      width: 100%;

      .el-input-number__decrease,
      .el-input-number__increase {
        background-color: #f5f7fa;
        border-color: #dcdfe6;

        &:hover {
          color: #409eff;
        }
      }
    }

    // 表格样式
    .el-table {
      margin-bottom: 60px;
      overflow: hidden;
      border-radius: 8px;

      th {
        font-weight: 600;
        color: #606266;
        background-color: #f5f7fa;
      }
    }

    // 按钮样式
    .el-button {
      &--primary {
        background-color: #409eff;
        border-color: #409eff;

        &:hover,
        &:focus {
          background-color: #66b1ff;
          border-color: #66b1ff;
        }
      }
    }
  }
</style>
