/**
 * @description Vue 2.7 Composition API 兼容性配置
 * 这个文件用于导出Vue 2.7内置的Composition API，方便在项目中使用
 */

// 从Vue中导出Composition API相关函数
import Vue from 'vue'

// 导出所有Composition API
export const {
  // 响应式核心
  reactive,
  ref,
  readonly,
  computed,
  watch,
  watchEffect,
  watchPostEffect,
  watchSyncEffect,

  // 生命周期钩子
  onBeforeMount,
  onMounted,
  onBeforeUpdate,
  onUpdated,
  onBeforeUnmount,
  onUnmounted,
  onErrorCaptured,
  onRenderTracked,
  onRenderTriggered,

  // 依赖注入
  provide,
  inject,

  // 工具函数
  isRef,
  unref,
  toRef,
  toRefs,
  isProxy,
  isReactive,
  isReadonly,

  // 高级API
  customRef,
  shallowRef,
  triggerRef,
  shallowReactive,
  shallowReadonly,
  markRaw,
  toRaw,

  // 效果作用域
  effectScope,
  getCurrentScope,
  onScopeDispose,
} = Vue

// 导出setup函数，用于在选项式API中使用Composition API
export function useSetup(setupFn) {
  const data = ref({})

  return {
    data,
    created() {
      const bindings = setupFn.call(this)
      if (bindings && typeof bindings === 'object') {
        Object.keys(bindings).forEach((key) => {
          data.value[key] = bindings[key]
        })
      }
    },
    computed: {
      ...Object.keys(data.value || {}).reduce((obj, key) => {
        obj[key] = function () {
          return data.value[key]
        }
        return obj
      }, {}),
    },
  }
}

// 导出一个辅助函数，用于在模板中使用ref
export function unwrapRef(ref) {
  return isRef(ref) ? ref.value : ref
}
