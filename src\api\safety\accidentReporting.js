import request from '@/utils/request'

// 分页获取列表
export function getAccidentReportingList(params) {
  return request({
    url: '/pc/productionSafetyAccident/list',
    method: 'get',
    params,
  })
}

// 根据ID获取
export function getAccidentReportingById(id) {
  return request({
    url: `/pc/productionSafetyAccident/${id}`,
    method: 'get',
  })
}

// 暂存
export function saveAccidentReporting(data) {
  return request({
    url: '/pc/productionSafetyAccident/draft',
    method: 'post',
    data,
  })
}

// 新增
export function addAccidentReporting(data) {
  return request({
    url: '/pc/productionSafetyAccident/submit',
    method: 'post',
    data,
  })
}

// 修改
export function updateAccidentReporting(data) {
  return request({
    url: '/pc/productionSafetyAccident/update',
    method: 'put',
    data,
  })
}

// 删除
export function delAccidentReporting(ids) {
  return request({
    url: '/pc/productionSafetyAccident/delete',
    method: 'delete',
    data: ids,
  })
}
