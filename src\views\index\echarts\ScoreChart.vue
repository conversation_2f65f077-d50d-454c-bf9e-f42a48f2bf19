<template>
  <div class="chart-container">
    <div ref="scoreChartRef" class="score-chart"></div>
    <div class="score-data">
      <div class="score-item">
        <div class="score-label">初始分</div>
        <div class="score-value">{{ initialScore }}</div>
      </div>
      <div class="score-item">
        <div class="score-label">
          <span class="dot gray-dot"></span>
          总分
        </div>
        <div class="score-value">{{ totalScore }}</div>
      </div>
      <div class="score-item">
        <div class="score-label">
          <span class="dot blue-dot"></span>
          加分
        </div>
        <div class="score-value">{{ addScore }}</div>
      </div>
      <div class="score-item">
        <div class="score-label">
          <span class="dot red-dot"></span>
          扣分
        </div>
        <div class="score-value">{{ minusScore }}</div>
      </div>
    </div>
  </div>
</template>

<script>
  import * as echarts from 'echarts'

  export default {
    name: 'ScoreChart',
    props: {
      initialScore: {
        type: Number,
        default: 1000,
      },
      totalScore: {
        type: Number,
        default: 900,
      },
      addScore: {
        type: Number,
        default: 200,
      },
      minusScore: {
        type: Number,
        default: 100,
      },
    },
    data() {
      return {
        chart: null,
      }
    },
    mounted() {
      this.$nextTick(() => {
        this.initChart()
      })
    },
    beforeDestroy() {
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    },
    methods: {
      initChart() {
        const chartDom = this.$refs.scoreChartRef
        if (!chartDom) return

        this.chart = echarts.init(chartDom)
        const option = {
          series: [
            {
              type: 'pie',
              radius: ['55%', '75%'],
              startAngle: 90,
              data: [
                {
                  value: this.totalScore,
                  name: '总分',
                  itemStyle: { color: '#f5f5f5' },
                },
                {
                  value: this.addScore,
                  name: '加分',
                  itemStyle: { color: '#4e7ae7' },
                },
                {
                  value: this.minusScore,
                  name: '扣分',
                  itemStyle: { color: '#ff4d4f' },
                },
              ],
              label: {
                show: false,
              },
              labelLine: {
                show: false,
              },
              itemStyle: {
                borderRadius: 100,
                borderWidth: 2,
                borderColor: '#fff',
              },
            },
          ],
        }
        this.chart.setOption(option)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .chart-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 100%;
    padding: 20px 40px;
  }

  .score-chart {
    width: 280px;
    height: 280px;
  }

  .score-data {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .score-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-width: 180px;

    .score-label {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #666;

      .dot {
        display: inline-block;
        width: 8px;
        height: 8px;
        margin-right: 8px;
        border-radius: 50%;
      }

      .blue-dot {
        background-color: #4e7ae7;
      }

      .red-dot {
        background-color: #ff4d4f;
      }
    }

    .score-value {
      font-size: 16px;
      font-weight: bold;
      color: #333;
    }
  }

  .score-item {
    .score-label {
      .dot {
        display: inline-block;
        width: 6px;
        height: 6px;
        margin-right: 6px;
        border-radius: 50%;
      }

      .gray-dot {
        background-color: #f5f5f5;
        border: 1px solid #e8e8e8;
      }

      .blue-dot {
        background-color: #4e7ae7;
      }

      .red-dot {
        background-color: #ff4d4f;
      }
    }
  }
</style>
