<template>
  <div class="test-navigation">
    <h2>导航测试页面</h2>

    <div class="section">
      <h3>主导航数据 (mainNavs)</h3>
      <pre>{{ JSON.stringify(mainNavs, null, 2) }}</pre>
    </div>

    <div class="section">
      <h3>当前激活的主导航 (activeMainNav)</h3>
      <p>{{ activeMainNav }}</p>
    </div>

    <div class="section">
      <h3>当前子导航数据 (currentSubNavs)</h3>
      <pre>{{ JSON.stringify(currentSubNavs, null, 2) }}</pre>
    </div>

    <div class="section">
      <h3>原始路由数据 (routes)</h3>
      <pre>{{ JSON.stringify(routes, null, 2) }}</pre>
    </div>

    <div class="section">
      <h3>测试主导航切换</h3>
      <el-button
        v-for="nav in mainNavs"
        :key="nav.name"
        :type="nav.name === activeMainNav ? 'primary' : 'default'"
        @click="switchMainNav(nav.name)"
      >
        {{ nav.title }}
      </el-button>
    </div>
  </div>
</template>

<script>
  import { mapGetters, mapActions } from 'vuex'

  export default {
    name: 'TestNavigation',
    computed: {
      ...mapGetters({
        routes: 'routes/routes',
        mainNavs: 'routes/mainNavs',
        activeMainNav: 'routes/activeMainNav',
        currentSubNavs: 'routes/currentSubNavs',
      }),
    },
    methods: {
      ...mapActions({
        switchMainNav: 'routes/switchMainNav',
      }),
    },
  }
</script>

<style lang="scss" scoped>
  .test-navigation {
    padding: 20px;

    .section {
      margin-bottom: 30px;

      h3 {
        margin-bottom: 10px;
        color: #409eff;
      }

      pre {
        max-height: 300px;
        padding: 15px;
        overflow-x: auto;
        font-size: 12px;
        background: #f5f5f5;
        border-radius: 4px;
      }
    }
  }
</style>
